/**
 * Test script to verify that follow-up system uses only customer-defined templates
 * and no longer uses the smart template system
 */

import { scheduleFollowUpFromAnalysis } from "./Chatbot API/utils/followUpScheduler.js";
import supabase from "./Chatbot API/utils/supabase.js";

async function testFollowUpTemplates() {
  console.log("🧪 Testing Follow-up Template System");
  console.log("=====================================");

  // Test data
  const testAuthId = "test-auth-id";
  const testContactId = "test-contact-id";
  const testPhoneNumber = "+60123456789";

  // Mock AI analysis that would trigger smart templates in the old system
  const mockAiAnalysis = {
    intent_category: "interested",
    engagement_score: 0.9, // High engagement (would trigger smart template additions)
    urgency_level: "immediate", // Would trigger urgency-based additions
    sentiment_score: 0.8,
    auto_tags: ["needs_follow_up"]
  };

  // Mock contact data
  const mockContact = {
    id: testContactId,
    name: "Test Customer",
    phone_number: testPhoneNumber,
    last_message_at: new Date().toISOString()
  };

  try {
    // First, create a test follow-up rule
    console.log("📝 Creating test follow-up rule...");
    
    const { data: rule, error: ruleError } = await supabase
      .from("follow_up_rules")
      .insert({
        auth_id: testAuthId,
        rule_name: "Test Rule",
        intent_category: "interested",
        delay_hours: 1,
        max_follow_ups: 1,
        message_template: "Hi {name}! This is a simple customer-defined template. No smart additions here!",
        is_active: true
      })
      .select()
      .single();

    if (ruleError) {
      console.error("❌ Error creating test rule:", ruleError);
      return;
    }

    console.log("✅ Test rule created successfully");

    // Now test the follow-up scheduling
    console.log("🚀 Testing follow-up scheduling...");
    
    const result = await scheduleFollowUpFromAnalysis(
      mockAiAnalysis,
      mockContact,
      testAuthId,
      testPhoneNumber,
      "auto_follow_up"
    );

    console.log("📊 Follow-up scheduling result:", result);

    if (result.success) {
      // Check the scheduled follow-up to see what template was used
      const { data: followUp, error: followUpError } = await supabase
        .from("scheduled_follow_ups")
        .select("message_template")
        .eq("contact_id", testContactId)
        .eq("auth_id", testAuthId)
        .order("created_at", { ascending: false })
        .limit(1)
        .single();

      if (followUpError) {
        console.error("❌ Error fetching scheduled follow-up:", followUpError);
      } else {
        console.log("📝 Template used:", followUp.message_template);
        
        // Verify it's the customer-defined template (no smart additions)
        const expectedTemplate = "Hi {name}! This is a simple customer-defined template. No smart additions here!";
        
        if (followUp.message_template === expectedTemplate) {
          console.log("✅ SUCCESS: Using customer-defined template only!");
          console.log("✅ No smart template additions detected");
        } else {
          console.log("❌ FAILURE: Template was modified or smart system still active");
          console.log("Expected:", expectedTemplate);
          console.log("Actual:", followUp.message_template);
        }
      }
    } else {
      console.log("❌ Follow-up scheduling failed:", result.reason || result.error);
    }

    // Clean up test data
    console.log("🧹 Cleaning up test data...");
    
    await supabase
      .from("scheduled_follow_ups")
      .delete()
      .eq("auth_id", testAuthId);
      
    await supabase
      .from("follow_up_rules")
      .delete()
      .eq("auth_id", testAuthId);

    console.log("✅ Test completed and cleaned up");

  } catch (error) {
    console.error("❌ Test failed with error:", error);
  }
}

// Run the test
testFollowUpTemplates().catch(console.error);
