import supabase from "./supabase.js";
import logger from "./logger.js";
import { sendWhatsAppTextMessage } from "./whatsapp.js";
import { analyzeContactConversation, shouldScheduleFollowUp, getFollowUpTemplate, getBooking<PERSON><PERSON><PERSON>Template, getSmartFollowUpRecommendation, INTENT_CATEGORIES, URGENCY_LEVELS } from "./aiTagging.js";
import { readFromGoogleSheets } from "./googleSheets.js";

/**
 * Enhanced follow-up scheduling and execution system
 * Handles intelligent automated follow-ups with 24-hour WhatsApp compliance,
 * smart timing, and personalized messaging based on AI analysis
 */

/**
 * Schedule a follow-up for a contact based on AI analysis
 * @param {string} contactId - Contact UUID
 * @param {string} authId - Business owner's auth ID
 * @param {string} phoneNumber - Customer's phone number
 * @param {Object} aiAnalysis - AI analysis results
 * @returns {Object} Scheduling result
 */
export async function scheduleFollowUpFromAnalysis(contactId, authId, phoneNumber, aiAnalysis) {
  try {
    logger.info(`📅 Evaluating smart follow-up scheduling | Contact: ${contactId} | Intent: ${aiAnalysis.intent_category} | Engagement: ${aiAnalysis.engagement_score} | Urgency: ${aiAnalysis.urgency_level || 'N/A'}`);

    // Get contact information
    const { data: contact, error: contactError } = await supabase
      .from("contacts")
      .select("*")
      .eq("id", contactId)
      .single();

    if (contactError || !contact) {
      logger.error("Error fetching contact for follow-up scheduling:", contactError);
      return { success: false, error: "Contact not found" };
    }

    // Check if follow-ups are enabled for this contact
    if (!contact.follow_up_enabled) {
      logger.info(`Follow-ups disabled for contact ${contactId}`);
      return { success: false, reason: "Follow-ups disabled for contact" };
    }

    // Use enhanced AI analysis to get smart follow-up recommendation
    const followUpRecommendation = getSmartFollowUpRecommendation(aiAnalysis, contact);

    if (!followUpRecommendation.shouldFollowUp) {
      logger.info(`Smart AI determined no follow-up needed | Contact: ${contactId} | Reason: ${followUpRecommendation.reason}`);
      return { success: false, reason: followUpRecommendation.reason };
    }

    // Get follow-up rules for this business and intent category
    let { data: followUpRule, error: ruleError } = await supabase
      .from("follow_up_rules")
      .select("*")
      .eq("auth_id", authId)
      .eq("intent_category", aiAnalysis.intent_category)
      .eq("is_active", true)
      .single();

    // If no rule found, create default rules for this user
    if (!followUpRule && ruleError?.code === 'PGRST116') {
      logger.info(`No follow-up rules found for ${authId}, creating default rules`);
      await createDefaultFollowUpRules(authId);

      // Try to get the rule again after creating defaults
      const { data: newRule } = await supabase
        .from("follow_up_rules")
        .select("*")
        .eq("auth_id", authId)
        .eq("intent_category", aiAnalysis.intent_category)
        .eq("is_active", true)
        .single();

      if (newRule) {
        followUpRule = newRule;
      }
    }

    // Use smart recommendation or rule settings with intelligent fallbacks
    const smartDelayHours = followUpRecommendation.delayHours || followUpRule?.delay_hours || 24;
    const maxFollowUps = followUpRule?.max_follow_ups || 1;

    // Use only customer-defined template from follow-up rules
    // If no rule exists, use a simple fallback template
    const messageTemplate = followUpRule?.message_template || 'Hi {name}! 👋 Just following up on our conversation. Is there anything else I can help you with?';

    // Enhanced WhatsApp 24-hour session window validation with smart timing
    const lastMessageTime = contact.last_message_at ? new Date(contact.last_message_at) : new Date();
    const now = new Date();
    const hoursSinceLastMessage = (now - lastMessageTime) / (1000 * 60 * 60);
    const hoursUntilWindowExpires = 24 - hoursSinceLastMessage;

    // Smart delay adjustment considering urgency and WhatsApp window
    let adjustedDelayHours = smartDelayHours;

    // For immediate urgency, try to send ASAP within window
    if (aiAnalysis.urgency_level === URGENCY_LEVELS.IMMEDIATE) {
      adjustedDelayHours = Math.min(1, Math.max(0.5, hoursUntilWindowExpires - 0.5));
    }
    // For high urgency, prioritize within window
    else if (aiAnalysis.urgency_level === URGENCY_LEVELS.HIGH) {
      adjustedDelayHours = Math.min(4, Math.max(1, hoursUntilWindowExpires - 1));
    }
    // For normal cases, respect the smart delay but adjust for window
    else if (smartDelayHours > hoursUntilWindowExpires && hoursUntilWindowExpires > 0) {
      adjustedDelayHours = Math.max(1, hoursUntilWindowExpires - 1);
      logger.info(`Adjusted smart follow-up delay from ${smartDelayHours}h to ${adjustedDelayHours}h to respect WhatsApp 24h window | Contact: ${phoneNumber}`);
    } else if (hoursUntilWindowExpires <= 0) {
      // 24-hour window has already expired, cannot send follow-up
      logger.info(`Cannot schedule follow-up - 24-hour WhatsApp window expired | Contact: ${phoneNumber} | Hours since last message: ${hoursSinceLastMessage.toFixed(1)}`);
      return { success: false, reason: "WhatsApp 24-hour messaging window expired" };
    }

    // Apply business hours optimization if available
    adjustedDelayHours = optimizeForBusinessHours(adjustedDelayHours, authId);

    // Check if contact has reached max follow-ups
    if (contact.follow_up_count >= maxFollowUps) {
      logger.info(`Contact ${contactId} has reached max follow-ups (${maxFollowUps})`);
      return { success: false, reason: "Max follow-ups reached" };
    }

    // Check if there's already a pending follow-up
    const { data: existingFollowUp, error: existingError } = await supabase
      .from("follow_up_schedules")
      .select("id")
      .eq("contact_id", contactId)
      .eq("status", "pending")
      .single();

    if (existingFollowUp) {
      logger.info(`Contact ${contactId} already has a pending follow-up`);
      return { success: false, reason: "Follow-up already scheduled" };
    }

    // Determine follow-up type based on intent (using valid database types)
    let followUpType = 'engagement_check'; // Default fallback

    if (aiAnalysis.intent_category === INTENT_CATEGORIES.BOOKING) {
      followUpType = 'booking_reminder';
    } else if (aiAnalysis.intent_category === INTENT_CATEGORIES.READY_TO_PURCHASE) {
      followUpType = 'purchase_assistance';
    } else if (aiAnalysis.intent_category === INTENT_CATEGORIES.INTERESTED ||
               aiAnalysis.intent_category === INTENT_CATEGORIES.PRODUCT_RESEARCH ||
               aiAnalysis.intent_category === INTENT_CATEGORIES.COMPARISON_SHOPPING) {
      followUpType = 'order_follow_up';
    } else if (aiAnalysis.intent_category === INTENT_CATEGORIES.PRICE_INQUIRY) {
      followUpType = 'price_follow_up';
    } else if (aiAnalysis.intent_category === INTENT_CATEGORIES.SUPPORT_INQUIRY) {
      followUpType = 'support_follow_up';
    }

    // Schedule the follow-up with adjusted delay
    const { data: scheduledFollowUp, error: scheduleError } = await supabase.rpc("schedule_follow_up", {
      p_contact_id: contactId,
      p_auth_id: authId,
      p_phone_number: phoneNumber,
      p_follow_up_type: followUpType,
      p_delay_hours: adjustedDelayHours,
      p_message_template: messageTemplate
    });

    if (scheduleError) {
      logger.error("Error scheduling follow-up:", scheduleError);
      return { success: false, error: scheduleError.message };
    }

    logger.info(`📅 Smart follow-up scheduled | Contact: ${contactId} | Type: ${followUpType} | Delay: ${adjustedDelayHours}h | Priority: ${followUpRecommendation.priority} | Follow-up ID: ${scheduledFollowUp}`);

    return {
      success: true,
      followUpId: scheduledFollowUp,
      followUpType,
      delayHours: adjustedDelayHours,
      priority: followUpRecommendation.priority,
      urgencyLevel: aiAnalysis.urgency_level,
      scheduledAt: new Date(Date.now() + adjustedDelayHours * 60 * 60 * 1000).toISOString(),
      reason: followUpRecommendation.reason
    };

  } catch (error) {
    logger.error("Error in follow-up scheduling:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Process pending follow-ups that are ready to be sent
 * @param {number} limit - Maximum number of follow-ups to process
 * @returns {Object} Processing results
 */
export async function processPendingFollowUps(limit = 50) {
  try {
    logger.info(`🔄 Processing pending follow-ups with enhanced efficiency (limit: ${limit})`);

    // Get pending follow-ups that are ready to be sent
    const { data: pendingFollowUps, error: fetchError } = await supabase.rpc("get_pending_follow_ups", {
      p_limit: limit
    });

    if (fetchError) {
      logger.error("Error fetching pending follow-ups:", fetchError);
      return { success: false, error: fetchError.message };
    }

    if (!pendingFollowUps || pendingFollowUps.length === 0) {
      logger.info("No pending follow-ups to process");
      return { success: true, processed: 0, successful: 0, failed: 0 };
    }

    logger.info(`Found ${pendingFollowUps.length} pending follow-ups to process`);

    // Group follow-ups by auth_id for batch processing efficiency
    const followUpsByAuth = groupFollowUpsByAuth(pendingFollowUps);

    // Process in parallel batches for better performance
    const batchSize = 10;
    const results = [];
    let totalProcessed = 0;
    let totalSuccessful = 0;
    let totalFailed = 0;

    for (const [authId, authFollowUps] of Object.entries(followUpsByAuth)) {
      // Get WhatsApp config once per auth_id for efficiency
      const { data: customer, error: customerError } = await supabase
        .from("whatsapp_customers")
        .select("*")
        .eq("auth_id", authId)
        .eq("is_active", true)
        .single();

      if (customerError || !customer) {
        logger.error(`No WhatsApp config found for auth_id: ${authId}`);
        // Mark all follow-ups for this auth as failed
        for (const followUp of authFollowUps) {
          results.push({
            followUpId: followUp.id,
            success: false,
            reason: "WhatsApp configuration not found"
          });
          totalFailed++;
        }
        continue;
      }

      // Process follow-ups for this auth in batches
      for (let i = 0; i < authFollowUps.length; i += batchSize) {
        const batch = authFollowUps.slice(i, i + batchSize);

        // Process batch in parallel
        const batchPromises = batch.map(followUp =>
          processIndividualFollowUp(followUp, customer)
        );

        const batchResults = await Promise.allSettled(batchPromises);

        // Collect results
        batchResults.forEach((result, index) => {
          totalProcessed++;
          if (result.status === 'fulfilled' && result.value.success) {
            totalSuccessful++;
            results.push(result.value);
          } else {
            totalFailed++;
            results.push({
              followUpId: batch[index].id,
              success: false,
              reason: result.status === 'rejected' ? result.reason : result.value.reason
            });
          }
        });
      }
    }

    logger.info(`✅ Enhanced batch processing complete | Total: ${totalProcessed} | Success: ${totalSuccessful} | Failed: ${totalFailed}`);
    return { success: true, processed: totalProcessed, successful: totalSuccessful, failed: totalFailed, results };

  } catch (error) {
    logger.error("Error processing pending follow-ups:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Stop follow-ups for a contact (when customer replies)
 * @param {string} contactId - Contact UUID
 * @param {string} reason - Reason for stopping follow-ups
 * @returns {boolean} Success status
 */
export async function stopFollowUpsForContact(contactId, reason = 'customer_replied') {
  try {
    const { error } = await supabase.rpc("stop_follow_ups", {
      p_contact_id: contactId,
      p_reason: reason
    });

    if (error) {
      logger.error("Error stopping follow-ups:", error);
      return false;
    }

    logger.info(`🛑 Follow-ups stopped for contact ${contactId} | Reason: ${reason}`);
    return true;

  } catch (error) {
    logger.error("Error stopping follow-ups:", error);
    return false;
  }
}

/**
 * Enable follow-ups for a contact
 * @param {string} contactId - Contact UUID
 * @returns {boolean} Success status
 */
export async function enableFollowUpsForContact(contactId) {
  try {
    const { error } = await supabase.rpc("enable_follow_ups", {
      p_contact_id: contactId
    });

    if (error) {
      logger.error("Error enabling follow-ups:", error);
      return false;
    }

    logger.info(`✅ Follow-ups enabled for contact ${contactId}`);
    return true;

  } catch (error) {
    logger.error("Error enabling follow-ups:", error);
    return false;
  }
}

/**
 * Create default follow-up rules for a new user
 * @param {string} authId - User's auth ID
 */
export async function createDefaultFollowUpRules(authId) {
  try {
    const defaultRules = [
      {
        auth_id: authId,
        rule_name: 'Interested Customer Follow-up',
        intent_category: 'interested',
        delay_hours: 4, // 4 hours - well within 24h window
        max_follow_ups: 1,
        message_template: 'Hi {name}! 👋 Just following up on our conversation. Do you have any other questions about our products/services? I\'m here to help! 😊',
        is_active: true // ENABLED by default - main follow-up for interested customers
      },
      {
        auth_id: authId,
        rule_name: 'Booking Reminder',
        intent_category: 'booking',
        delay_hours: 2, // 2 hours - urgent for bookings
        max_follow_ups: 2,
        message_template: 'Hi {name}! 📅 Following up on your booking inquiry. Would you like to proceed with scheduling? Let me know what works best for you!',
        is_active: false // DISABLED by default - only enable if needed
      },
      {
        auth_id: authId,
        rule_name: 'Order Follow-up',
        intent_category: 'placed_order',
        delay_hours: 1, // 1 hour - quick confirmation
        max_follow_ups: 1,
        message_template: 'Hi {name}! 📦 Thank you for your order! Is there anything else I can help you with? We appreciate your business! 🙏',
        is_active: false // DISABLED by default - only enable if needed
      }
    ];

    // Use upsert to handle existing rules gracefully
    const { data, error } = await supabase
      .from("follow_up_rules")
      .upsert(defaultRules, {
        onConflict: 'auth_id,intent_category',
        ignoreDuplicates: true
      })
      .select();

    if (error) {
      logger.error("Error creating default follow-up rules:", error);
      return false;
    }

    logger.info(`✅ Created ${data.length} default follow-up rules for user ${authId}`);
    return true;

  } catch (error) {
    logger.error("Error in createDefaultFollowUpRules:", error);
    return false;
  }
}

/**
 * Process booking reminders from Google Sheets data
 * @param {string} authId - User's auth ID
 * @param {Object} customer - Customer configuration with Google Sheets details
 */
export async function processBookingRemindersFromSheets(authId, customer) {
  try {
    if (!customer.spreadsheet_id) {
      logger.info(`No Google Sheets configured for customer ${authId}, skipping booking reminders`);
      return { success: false, reason: 'No Google Sheets configured' };
    }

    // Read all data from Google Sheets
    const sheetData = await readFromGoogleSheets(customer);

    if (!sheetData || sheetData.length === 0) {
      logger.info(`No data found in Google Sheets for customer ${authId}`);
      return { success: false, reason: 'No data in sheets' };
    }

    // First row should contain headers
    const headers = sheetData[0];
    const rows = sheetData.slice(1);

    // Find relevant columns
    const phoneColumnIndex = headers.findIndex(header =>
      header.toLowerCase().includes('phone') ||
      header.toLowerCase().includes('number') ||
      header.toLowerCase().includes('contact')
    );

    const dateColumnIndex = headers.findIndex(header =>
      header.toLowerCase().includes('date') ||
      header.toLowerCase().includes('appointment') ||
      header.toLowerCase().includes('booking') ||
      header.toLowerCase().includes('schedule')
    );

    const statusColumnIndex = headers.findIndex(header =>
      header.toLowerCase().includes('status') ||
      header.toLowerCase().includes('confirmed') ||
      header.toLowerCase().includes('pending')
    );

    const nameColumnIndex = headers.findIndex(header =>
      header.toLowerCase().includes('name') ||
      header.toLowerCase().includes('customer')
    );

    if (phoneColumnIndex === -1 || dateColumnIndex === -1) {
      logger.warn(`Required columns not found in Google Sheets for customer ${authId}`);
      return { success: false, reason: 'Required columns not found' };
    }

    const now = new Date();
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const dayAfterTomorrow = new Date(now.getTime() + 48 * 60 * 60 * 1000);

    let remindersProcessed = 0;
    const results = [];

    // Process each row for potential booking reminders
    for (const row of rows) {
      try {
        const phoneNumber = row[phoneColumnIndex];
        const bookingDate = row[dateColumnIndex];
        const status = statusColumnIndex !== -1 ? row[statusColumnIndex] : '';
        const customerName = nameColumnIndex !== -1 ? row[nameColumnIndex] : '';

        if (!phoneNumber || !bookingDate) {
          continue;
        }

        // Parse booking date
        const parsedDate = new Date(bookingDate);
        if (isNaN(parsedDate.getTime())) {
          continue;
        }

        // Check if booking is within reminder window (24-48 hours ahead)
        const isWithinReminderWindow = parsedDate >= tomorrow && parsedDate <= dayAfterTomorrow;

        // Check if status indicates confirmed/pending booking
        const isValidStatus = !status ||
          status.toLowerCase().includes('confirmed') ||
          status.toLowerCase().includes('pending') ||
          status.toLowerCase().includes('scheduled');

        if (isWithinReminderWindow && isValidStatus) {
          // Check if we already sent a reminder for this booking recently
          const { data: recentReminder } = await supabase
            .from("scheduled_follow_ups")
            .select("id")
            .eq("auth_id", authId)
            .eq("phone_number", phoneNumber)
            .eq("follow_up_type", "booking_reminder")
            .gte("created_at", new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString())
            .limit(1);

          if (!recentReminder || recentReminder.length === 0) {
            // Create booking reminder data with all available information
            const bookingDetails = {
              date: parsedDate.toLocaleDateString(),
              time: parsedDate.toLocaleTimeString(),
              customerName: customerName || 'Valued Customer',
              status: status || 'Confirmed'
            };

            // Add any additional fields from the row
            headers.forEach((header, index) => {
              const value = row[index];
              if (value && value.toString().trim()) {
                const lowerHeader = header.toLowerCase();
                if (lowerHeader.includes('service') || lowerHeader.includes('product')) {
                  bookingDetails.service = value;
                } else if (lowerHeader.includes('location') || lowerHeader.includes('address')) {
                  bookingDetails.location = value;
                } else if (lowerHeader.includes('note') || lowerHeader.includes('remark') || lowerHeader.includes('comment')) {
                  bookingDetails.notes = value;
                }
              }
            });

            // Get contact information for enhanced template
            const { data: contact } = await supabase
              .from("contacts")
              .select("name")
              .eq("auth_id", authId)
              .eq("phone_number", phoneNumber)
              .single();

            // Generate enhanced booking reminder message
            const reminderMessage = getBookingReminderTemplate(bookingDetails, contact);

            // Schedule immediate booking reminder
            const { data: scheduledReminder, error: scheduleError } = await supabase.rpc("schedule_follow_up", {
              p_contact_id: null, // Will be resolved when sending
              p_auth_id: authId,
              p_phone_number: phoneNumber,
              p_follow_up_type: 'booking_reminder',
              p_delay_hours: 0, // Send immediately
              p_message_template: reminderMessage
            });

            if (scheduleError) {
              logger.error(`Error scheduling booking reminder for ${phoneNumber}:`, scheduleError);
              results.push({
                phoneNumber,
                success: false,
                error: scheduleError.message
              });
            } else {
              remindersProcessed++;
              results.push({
                phoneNumber,
                success: true,
                bookingDate: parsedDate,
                reminderScheduled: true
              });
              logger.info(`📅 Scheduled booking reminder for ${phoneNumber} - Booking: ${parsedDate.toLocaleDateString()}`);
            }
          }
        }
      } catch (rowError) {
        logger.error(`Error processing booking reminder row:`, rowError);
      }
    }

    logger.info(`✅ Processed ${remindersProcessed} booking reminders from Google Sheets for customer ${authId}`);
    return {
      success: true,
      remindersProcessed,
      results
    };

  } catch (error) {
    logger.error("Error processing booking reminders from sheets:", error);
    return { success: false, error: error.message };
  }
}



/**
 * Optimize follow-up timing for business hours
 * @param {number} delayHours - Original delay in hours
 * @param {string} authId - Business owner's auth ID
 * @returns {number} Optimized delay in hours
 */
function optimizeForBusinessHours(delayHours, authId) {
  // For now, return the original delay
  // TODO: Implement business hours optimization based on user settings
  // This would check the user's business hours and adjust timing accordingly

  const now = new Date();
  const scheduledTime = new Date(now.getTime() + delayHours * 60 * 60 * 1000);
  const hour = scheduledTime.getHours();

  // Basic optimization: avoid very early morning or late night
  if (hour < 8) {
    // If scheduled before 8 AM, delay to 9 AM
    const hoursToAdd = 9 - hour;
    return delayHours + hoursToAdd;
  } else if (hour > 21) {
    // If scheduled after 9 PM, delay to next day 9 AM
    const hoursToAdd = 24 - hour + 9;
    return delayHours + hoursToAdd;
  }

  return delayHours;
}

/**
 * Group follow-ups by auth_id for batch processing efficiency
 * @param {Array} followUps - Array of follow-up objects
 * @returns {Object} Follow-ups grouped by auth_id
 */
function groupFollowUpsByAuth(followUps) {
  return followUps.reduce((groups, followUp) => {
    const authId = followUp.auth_id;
    if (!groups[authId]) {
      groups[authId] = [];
    }
    groups[authId].push(followUp);
    return groups;
  }, {});
}

/**
 * Process individual follow-up with enhanced error handling
 * @param {Object} followUp - Follow-up object
 * @param {Object} customer - WhatsApp customer configuration
 * @returns {Object} Processing result
 */
async function processIndividualFollowUp(followUp, customer) {
  try {
    // Skip if follow-ups are disabled for this contact
    if (!followUp.contact_follow_up_enabled) {
      logger.info(`Skipping follow-up for contact ${followUp.contact_id} - follow-ups disabled`);

      // Cancel this follow-up
      await supabase
        .from("follow_up_schedules")
        .update({ status: "cancelled", updated_at: new Date().toISOString() })
        .eq("id", followUp.id);

      return {
        followUpId: followUp.id,
        success: false,
        reason: "Follow-ups disabled for contact"
      };
    }

    // Validate 24-hour WhatsApp messaging window before sending
    const lastMessageTime = new Date(followUp.contact_last_message_at || new Date());
    const now = new Date();
    const hoursSinceLastMessage = (now - lastMessageTime) / (1000 * 60 * 60);

    if (hoursSinceLastMessage >= 24) {
      logger.warn(`Cannot send follow-up - 24-hour WhatsApp window expired | Contact: ${followUp.phone_number} | Hours since last message: ${hoursSinceLastMessage.toFixed(1)}`);

      // Mark follow-up as cancelled due to expired window
      await supabase.rpc("mark_follow_up_cancelled", {
        p_follow_up_id: followUp.id,
        p_reason: "24_hour_window_expired"
      });

      return {
        followUpId: followUp.id,
        success: false,
        reason: "24-hour WhatsApp messaging window expired"
      };
    }

    // Process message template (replace placeholders)
    const customerName = followUp.contact_name || 'there';
    const processedMessage = followUp.message_template.replace('{name}', customerName);

    // Send WhatsApp message
    const messageResult = await sendWhatsAppTextMessage(
      followUp.phone_number,
      processedMessage,
      customer.whatsapp_phone_number_id,
      customer.system_access_token
    );

    if (messageResult && messageResult.messages && messageResult.messages[0]) {
      // Mark follow-up as executed
      const { error: markError } = await supabase.rpc("mark_follow_up_executed", {
        p_follow_up_id: followUp.id,
        p_whatsapp_message_id: messageResult.messages[0].id,
        p_message_sent: processedMessage
      });

      if (markError) {
        logger.error("Error marking follow-up as executed:", markError);
      }

      // Store in chat history
      await supabase.from("chat_history").insert([
        {
          auth_id: followUp.auth_id,
          phone_number: followUp.phone_number,
          role: "assistant",
          content: processedMessage,
        },
      ]);

      // Template performance tracking removed - using customer-defined templates only

      logger.info(`✅ Follow-up sent successfully | Contact: ${followUp.contact_id} | Phone: ${followUp.phone_number}`);

      return {
        followUpId: followUp.id,
        success: true,
        messageId: messageResult.messages[0].id,
        reason: "Message sent successfully"
      };

    } else {
      logger.error(`Failed to send follow-up message | Contact: ${followUp.contact_id} | Phone: ${followUp.phone_number}`);

      // Mark follow-up as failed
      await supabase.rpc("mark_follow_up_failed", {
        p_follow_up_id: followUp.id,
        p_reason: "WhatsApp API error"
      });

      return {
        followUpId: followUp.id,
        success: false,
        reason: "Failed to send WhatsApp message"
      };
    }

  } catch (error) {
    logger.error(`Error processing follow-up ${followUp.id}:`, error);

    // Mark follow-up as failed
    try {
      await supabase.rpc("mark_follow_up_failed", {
        p_follow_up_id: followUp.id,
        p_reason: error.message
      });
    } catch (markError) {
      logger.error("Error marking follow-up as failed:", markError);
    }

    return {
      followUpId: followUp.id,
      success: false,
      reason: error.message
    };
  }
}


