import express from "express";
import OpenAI from "openai";
import supabase from "../utils/supabase.js";
import { google } from "googleapis";
import axios from "axios";
import dotenv from "dotenv";
import fs from "fs";
import path from "path";
import os from "os";
import { Readable } from "stream";
import multer from "multer";
import FormData from "form-data";
// FFmpeg dependency removed - using native Whisper format support
import {
  getPaginationParams,
  buildPaginatedResponse,
} from "../utils/pagination.js";
import logger from "../utils/logger.js";
import { readFromGoogleSheets, findOrderInGoogleSheets, getOrderStatusFromGoogleSheets, saveToGoogleSheets } from '../utils/googleSheets.js';
import {
  getCustomerConfig,
  findCustomerByPhoneNumber,
  checkUserQuotas,
} from "../utils/database.js";
import { analyzeContactConversation, INTENT_CATEGORIES, AUTO_TAGS } from "../utils/aiTagging.js";
import { scheduleFollowUpFromAnalysis, stopFollowUpsForContact, enableFollowUpsForContact } from "../utils/followUpScheduler.js";
import { getFollowUpAnalytics, getFollowUpDashboard, trackFollowUpOutcome } from "../utils/followUpAnalytics.js";

import { triggerFollowUpProcessing } from "../utils/followUpProcessor.js";
import {
  getSocialMediaIntegrations,
  getPlatformIntegration,
  saveSocialMediaIntegration,
  togglePlatformIntegration,
  findCustomerByPlatformId,
  validatePlatformCredentials
} from "../utils/socialMediaIntegration.js";
import {
  sendMessengerTextMessage,
  processMessengerMessage,
  validateMessengerCredentials
} from "../utils/messenger.js";
import {
  sendInstagramTextMessage,
  processInstagramMessage,
  validateInstagramCredentials
} from "../utils/instagram.js";
import {
  getGoogleIntegrations,
  getGoogleServiceIntegration,
  saveGoogleIntegration,
  toggleGoogleIntegration,
  validateGoogleSheetsAccess,
  extractSpreadsheetIdFromUrl
} from "../utils/googleIntegrations.js";
import { migrateWhatsAppCustomers } from "../utils/socialMediaIntegration.js";
import {
  generateOAuthURL,
  exchangeCodeForToken,
  getLongLivedToken,
  getUserPages,
  getUserInstagramAccounts,
  getUserWhatsAppAccounts
} from "../utils/metaOAuth.js";
import {
  refreshExpiredTokens,
  refreshUserToken,
  getTokenStatus,
  cleanupExpiredOAuthStates
} from "../utils/tokenRefresh.js";

// Add polyfill for File global (required by OpenAI SDK for Node.js compatibility)
// This creates a proper File implementation that works with the OpenAI SDK
if (typeof globalThis.File === 'undefined') {
  globalThis.File = class File {
    constructor(fileBits, fileName, options = {}) {
      this.name = fileName;
      this.lastModified = options.lastModified || Date.now();
      this.type = options.type || '';
      
      // Handle different input types (Buffer, Array, etc.)
      if (Array.isArray(fileBits)) {
        // Concatenate all bits into a single buffer
        const totalSize = fileBits.reduce((size, bit) => {
          if (Buffer.isBuffer(bit)) return size + bit.length;
          if (typeof bit === 'string') return size + Buffer.byteLength(bit);
          if (bit instanceof ArrayBuffer) return size + bit.byteLength;
          return size + (bit.length || bit.size || 0);
        }, 0);
        
        this._buffer = Buffer.concat(
          fileBits.map(bit => {
            if (Buffer.isBuffer(bit)) return bit;
            if (typeof bit === 'string') return Buffer.from(bit);
            if (bit instanceof ArrayBuffer) return Buffer.from(bit);
            return Buffer.from(bit);
          })
        );
      } else if (Buffer.isBuffer(fileBits)) {
        this._buffer = fileBits;
      } else {
        this._buffer = Buffer.from(fileBits);
      }
      
      this.size = this._buffer.length;
    }
    
    stream() {
      // Return a readable stream for the buffer
      return Readable.from(this._buffer);
    }
    
    async arrayBuffer() {
      // Return ArrayBuffer representation
      return this._buffer.buffer.slice(
        this._buffer.byteOffset,
        this._buffer.byteOffset + this._buffer.byteLength
      );
    }
    
    async text() {
      return this._buffer.toString('utf-8');
    }
    
    // Add Symbol.toStringTag for proper type detection
    get [Symbol.toStringTag]() {
      return 'File';
    }
    
    // Make it work with FormData and fetch
    [Symbol.iterator]() {
      return this._buffer[Symbol.iterator]();
    }
  };
}

const router = express.Router();
// Load environment variables
dotenv.config();

// Configure multer for file uploads
const storage = multer.memoryStorage();

// MIME type mapping for WhatsApp compatibility
const mimeTypeMapping = {
  // Audio formats
  'audio/mp3': 'audio/mpeg',
  'audio/x-mp3': 'audio/mpeg',
  'audio/mpeg3': 'audio/mpeg',
  'audio/x-mpeg3': 'audio/mpeg',
  'audio/x-m4a': 'audio/aac',
  'audio/wav': 'audio/mpeg',
  'audio/x-wav': 'audio/mpeg',
  'audio/wave': 'audio/mpeg',
  'audio/x-pn-wav': 'audio/mpeg',
  'audio/vnd.wave': 'audio/mpeg',
  'audio/x-aac': 'audio/aac',
  'audio/basic': 'audio/mpeg',
  'audio/x-basic': 'audio/mpeg',
  'audio/x-mpeg': 'audio/mpeg',
  'audio/3gpp': 'audio/amr',
  'audio/3gpp2': 'audio/amr',
  // MediaRecorder formats - these are the common outputs from browser recording
  'audio/webm': 'audio/ogg',
  'audio/webm;codecs=opus': 'audio/ogg',
  'audio/ogg;codecs=opus': 'audio/ogg',
  'audio/mp4;codecs=aac': 'audio/aac',
  
  // Video formats
  'video/quicktime': 'video/mp4',
  'video/x-msvideo': 'video/mp4',
  'video/avi': 'video/mp4',
  'video/msvideo': 'video/mp4',
  'video/x-ms-wmv': 'video/mp4',
  'video/mpeg': 'video/mp4',
  'video/x-mpeg': 'video/mp4',
  'video/x-matroska': 'video/mp4',
  'video/webm': 'video/mp4',
  'video/x-flv': 'video/mp4',
  'video/3gpp': 'video/3gpp',
  'video/3gpp2': 'video/3gpp',
  
  // Image formats (mostly for completeness)
  'image/jpg': 'image/jpeg',
  'image/pjpeg': 'image/jpeg',
  'image/x-png': 'image/png'
};

// File filter for media files
const fileFilter = (req, file, cb) => {
  // Preferred image formats first
  const preferredTypes = {
    'image/jpeg': true,
    'image/jpg': true,
    'image/png': true,
  };

  // Other supported formats
  const otherTypes = {
    'image/gif': true,
    'image/webp': true,
    'audio/mpeg': true,
    'audio/mp3': true,
    'audio/aac': true,
    'audio/ogg': true,
    'audio/opus': true,
    'audio/amr': true,
    'audio/webm': true,
    'audio/mp4': true,
    'video/mp4': true,
    'video/3gpp': true,
  };

  // Map MIME type if needed
  const mappedMimeType = mimeTypeMapping[file.mimetype] || file.mimetype;
  file.mappedMimeType = mappedMimeType; // Store mapped MIME type for later use

  // Check if it's a preferred format
  if (preferredTypes[mappedMimeType]) {
    cb(null, true);
    return;
  }

  // For WebP images, suggest converting to JPEG/PNG
  if (mappedMimeType === 'image/webp') {
    cb(new Error('WebP format is not recommended. Please convert to JPEG or PNG for better compatibility.'), false);
    return;
  }

  // Check other supported formats
  if (otherTypes[mappedMimeType]) {
    cb(null, true);
    return;
  }

  cb(new Error('Invalid file type. Supported formats: JPEG, PNG (recommended for images), MP3/MPEG/OGG/WebM (for audio), MP4 (for video)'), false);
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 16 * 1024 * 1024, // 16MB limit (WhatsApp's limit)
    files: 1 // Only allow one file at a time
  }
});

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

router.post("/add-knowledge", async (req, res) => {
  try {
    const { content, authId, title } = req.body;

    if (!content || !authId) {
      return res.status(400).json({
        error: "Missing required fields: content, authId",
      });
    }

    // Generate embedding for the content
    const embeddingResponse = await openai.embeddings.create({
      model: "text-embedding-3-small",
      input: content,
    });

    const embedding = embeddingResponse.data[0].embedding;

    // Store in knowledge base
    const { data, error } = await supabase
      .from("knowledge_base")
      .insert({
        auth_id: authId,
        content: content,
        title: title || "Untitled",
        embedding: embedding,
        has_image: false, // Default to no image for text-only entries
      })
      .select("id, title, content, created_at, has_image");

    if (error) throw error;

    res.json({
      success: true,
      message: "Knowledge base entry added successfully",
      data: data[0],
    });
  } catch (error) {
    console.error("Error adding knowledge:", error);
    res.status(500).json({ error: "Failed to add knowledge base entry" });
  }
});

// Add knowledge base entry with image support
router.post("/add-knowledge-with-image", upload.single('image'), async (req, res) => {
  try {
    const { content, authId, title } = req.body;

    if (!content || !authId) {
      return res.status(400).json({
        error: "Missing required fields: content, authId",
      });
    }

    // Generate embedding for the content
    const embeddingResponse = await openai.embeddings.create({
      model: "text-embedding-3-small",
      input: content,
    });

    const embedding = embeddingResponse.data[0].embedding;

    let imageData = null;
    
    // Handle image upload if provided
    if (req.file) {
      // Validate image type
      if (!req.file.mimetype.startsWith('image/')) {
        return res.status(400).json({
          error: "Invalid file type. Only images are allowed.",
        });
      }

      // Generate unique filename for the image
      const timestamp = Date.now();
      const random = Math.round(Math.random() * 1E9);
      const ext = path.extname(req.file.originalname);
      const filename = `kb-image-${timestamp}-${random}${ext}`;

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase
        .storage
        .from('whatsapp-media')
        .upload(`${authId}/knowledge-base/${filename}`, req.file.buffer, {
          contentType: req.file.mimetype,
          cacheControl: '31536000',
          upsert: false
        });

      if (uploadError) {
        console.error("Error uploading image to Supabase Storage:", uploadError);
        return res.status(500).json({ error: "Failed to upload image" });
      }

      // Get public URL
      const { data: { publicUrl } } = supabase
        .storage
        .from('whatsapp-media')
        .getPublicUrl(`${authId}/knowledge-base/${filename}`);

      imageData = {
        has_image: true,
        image_filename: filename,
        image_url: publicUrl,
        image_mime_type: req.file.mimetype,
        image_file_size: req.file.size
      };
    }

    // Store in knowledge base
    const insertData = {
      auth_id: authId,
      content: content,
      title: title || "Untitled",
      embedding: embedding,
      has_image: imageData ? true : false,
      ...(imageData && {
        image_filename: imageData.image_filename,
        image_url: imageData.image_url,
        image_mime_type: imageData.image_mime_type,
        image_file_size: imageData.image_file_size
      })
    };

    const { data, error } = await supabase
      .from("knowledge_base")
      .insert(insertData)
      .select("id, title, content, created_at, has_image, image_filename, image_url, image_mime_type, image_file_size");

    if (error) throw error;

    res.json({
      success: true,
      message: "Knowledge base entry added successfully",
      data: data[0],
    });
  } catch (error) {
    console.error("Error adding knowledge with image:", error);
    res.status(500).json({ error: "Failed to add knowledge base entry with image" });
  }
});

router.put("/update-knowledge", async (req, res) => {
  try {
    const { id, content, authId, title } = req.body;

    if (!id || !content || !authId) {
      return res.status(400).json({
        error: "Missing required fields: id, content, authId",
      });
    }

    // Generate new embedding for the updated content
    const embeddingResponse = await openai.embeddings.create({
      model: "text-embedding-3-small",
      input: content,
    });

    const embedding = embeddingResponse.data[0].embedding;

    // Update the knowledge base entry
    const { data, error } = await supabase
      .from("knowledge_base")
      .update({
        content: content,
        title: title || "Untitled",
        embedding: embedding,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .eq("auth_id", authId)
      .select("id, title, content, updated_at, has_image, image_filename, image_url, image_mime_type, image_file_size");

    if (error) throw error;

    if (data.length === 0) {
      return res.status(404).json({
        error:
          "Knowledge base entry not found or you do not have permission to update it",
      });
    }

    res.json({
      success: true,
      message: "Knowledge base entry updated successfully",
      data: data[0],
    });
  } catch (error) {
    console.error("Error updating knowledge:", error);
    res.status(500).json({ error: "Failed to update knowledge base entry" });
  }
});

// Update knowledge base entry with image support
router.put("/update-knowledge-with-image", upload.single('image'), async (req, res) => {
  try {
    const { id, content, authId, title, removeImage } = req.body;

    if (!id || !content || !authId) {
      return res.status(400).json({
        error: "Missing required fields: id, content, authId",
      });
    }

    // Get existing entry to handle image deletion
    const { data: existingEntry, error: fetchError } = await supabase
      .from("knowledge_base")
      .select("id, image_filename, image_url, has_image")
      .eq("id", id)
      .eq("auth_id", authId)
      .single();

    if (fetchError || !existingEntry) {
      return res.status(404).json({
        error: "Knowledge base entry not found or you do not have permission to update it",
      });
    }

    // Generate new embedding for the updated content
    const embeddingResponse = await openai.embeddings.create({
      model: "text-embedding-3-small",
      input: content,
    });

    const embedding = embeddingResponse.data[0].embedding;

    let imageData = {
      has_image: existingEntry.has_image,
      image_filename: existingEntry.image_filename,
      image_url: existingEntry.image_url,
      image_mime_type: null,
      image_file_size: null
    };

    // Handle image removal
    if (removeImage === 'true' && existingEntry.has_image) {
      // Delete existing image from storage
      if (existingEntry.image_filename) {
        const { error: deleteError } = await supabase
          .storage
          .from('whatsapp-media')
          .remove([`${authId}/knowledge-base/${existingEntry.image_filename}`]);

        if (deleteError) {
          console.error("Error deleting existing image:", deleteError);
        }
      }

      imageData = {
        has_image: false,
        image_filename: null,
        image_url: null,
        image_mime_type: null,
        image_file_size: null
      };
    }

    // Handle new image upload
    if (req.file) {
      // Validate image type
      if (!req.file.mimetype.startsWith('image/')) {
        return res.status(400).json({
          error: "Invalid file type. Only images are allowed.",
        });
      }

      // Delete existing image if updating
      if (existingEntry.has_image && existingEntry.image_filename) {
        const { error: deleteError } = await supabase
          .storage
          .from('whatsapp-media')
          .remove([`${authId}/knowledge-base/${existingEntry.image_filename}`]);

        if (deleteError) {
          console.error("Error deleting existing image:", deleteError);
        }
      }

      // Generate unique filename for the new image
      const timestamp = Date.now();
      const random = Math.round(Math.random() * 1E9);
      const ext = path.extname(req.file.originalname);
      const filename = `kb-image-${timestamp}-${random}${ext}`;

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase
        .storage
        .from('whatsapp-media')
        .upload(`${authId}/knowledge-base/${filename}`, req.file.buffer, {
          contentType: req.file.mimetype,
          cacheControl: '31536000',
          upsert: false
        });

      if (uploadError) {
        console.error("Error uploading image to Supabase Storage:", uploadError);
        return res.status(500).json({ error: "Failed to upload image" });
      }

      // Get public URL
      const { data: { publicUrl } } = supabase
        .storage
        .from('whatsapp-media')
        .getPublicUrl(`${authId}/knowledge-base/${filename}`);

      imageData = {
        has_image: true,
        image_filename: filename,
        image_url: publicUrl,
        image_mime_type: req.file.mimetype,
        image_file_size: req.file.size
      };
    }

    // Update the knowledge base entry
    const { data, error } = await supabase
      .from("knowledge_base")
      .update({
        content: content,
        title: title || "Untitled",
        embedding: embedding,
        has_image: imageData.has_image,
        image_filename: imageData.image_filename,
        image_url: imageData.image_url,
        image_mime_type: imageData.image_mime_type,
        image_file_size: imageData.image_file_size,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .eq("auth_id", authId)
      .select("id, title, content, updated_at, has_image, image_filename, image_url, image_mime_type, image_file_size");

    if (error) throw error;

    if (data.length === 0) {
      return res.status(404).json({
        error:
          "Knowledge base entry not found or you do not have permission to update it",
      });
    }

    res.json({
      success: true,
      message: "Knowledge base entry updated successfully",
      data: data[0],
    });
  } catch (error) {
    console.error("Error updating knowledge with image:", error);
    res.status(500).json({ error: "Failed to update knowledge base entry with image" });
  }
});

router.delete("/delete-knowledge", async (req, res) => {
  try {
    const { id, authId } = req.body;

    if (!id || !authId) {
      return res.status(400).json({
        error: "Missing required fields: id, authId",
      });
    }

    // First, get the entry to check if it has an image
    const { data: entryToDelete, error: fetchError } = await supabase
      .from("knowledge_base")
      .select("id, title, has_image, image_filename")
      .eq("id", id)
      .eq("auth_id", authId)
      .single();

    if (fetchError || !entryToDelete) {
      return res.status(404).json({
        error: "Knowledge base entry not found or you do not have permission to delete it",
      });
    }

    // Delete associated image if it exists
    if (entryToDelete.has_image && entryToDelete.image_filename) {
      const { error: deleteImageError } = await supabase
        .storage
        .from('whatsapp-media')
        .remove([`${authId}/knowledge-base/${entryToDelete.image_filename}`]);

      if (deleteImageError) {
        console.error("Error deleting associated image:", deleteImageError);
        // Don't fail the entire operation if image deletion fails
      }
    }

    // Delete the knowledge base entry
    const { data, error } = await supabase
      .from("knowledge_base")
      .delete()
      .eq("id", id)
      .eq("auth_id", authId)
      .select("id, title");

    if (error) throw error;

    if (data.length === 0) {
      return res.status(404).json({
        error:
          "Knowledge base entry not found or you do not have permission to delete it",
      });
    }

    res.json({
      success: true,
      message: "Knowledge base entry deleted successfully",
      data: data[0],
    });
  } catch (error) {
    console.error("Error deleting knowledge:", error);
    res.status(500).json({ error: "Failed to delete knowledge base entry" });
  }
});

// Send knowledge base image to customer
router.post("/send-knowledge-image", async (req, res) => {
  try {
    const { authId, phoneNumber, knowledgeId, caption } = req.body;

    if (!authId || !phoneNumber || !knowledgeId) {
      return res.status(400).json({
        error: "Missing required fields: authId, phoneNumber, knowledgeId",
      });
    }

    // Get the knowledge base entry
    const { data: knowledgeEntry, error: fetchError } = await supabase
      .from("knowledge_base")
      .select("id, title, has_image, image_filename, image_url, image_mime_type")
      .eq("id", knowledgeId)
      .eq("auth_id", authId)
      .single();

    if (fetchError || !knowledgeEntry) {
      return res.status(404).json({
        error: "Knowledge base entry not found or you do not have permission to access it",
      });
    }

    if (!knowledgeEntry.has_image || !knowledgeEntry.image_url) {
      return res.status(400).json({
        error: "This knowledge base entry does not have an image",
      });
    }

    // Get customer WhatsApp configuration
    let customer;
    try {
      customer = await getCustomerConfig(authId);
    } catch (error) {
      return res.status(404).json({
        error: "WhatsApp configuration not found. Please set up your WhatsApp integration first.",
      });
    }

    // Check user quotas
    let quotaCheck;
    try {
      quotaCheck = await checkUserQuotas(authId);
    } catch (error) {
      return res.status(500).json({ error: "Failed to check user quota" });
    }

    const quotaInfo = quotaCheck[0];

    if (!quotaInfo || !quotaInfo.subscription_active) {
      return res.status(403).json({
        error: quotaInfo?.subscription_status === "expired"
          ? "Your subscription has expired. Please renew to continue using the chatbot."
          : "No active subscription found. Please subscribe to use the chatbot.",
      });
    }

    if (!quotaInfo.can_send_message) {
      return res.status(429).json({
        error: `You have reached your monthly message limit (${quotaInfo.current_message_count}/${quotaInfo.message_limit}). Please upgrade your plan or wait for next month.`,
      });
    }

    // Send the image message
    const messageResult = await sendWhatsAppImageMessage(
      phoneNumber,
      knowledgeEntry.image_url,
      customer.whatsapp_phone_number_id,
      customer.system_access_token,
      caption || `Image from knowledge base: ${knowledgeEntry.title}`,
      customer
    );

    // Update contact information
    await supabase.rpc("upsert_contact", {
      p_auth_id: authId,
      p_phone_number: phoneNumber,
    });

    // Store the sent message in chat history
    await supabase.from("chat_history").insert({
      auth_id: authId,
      phone_number: phoneNumber,
      role: "assistant",
      content: caption || `Image from knowledge base: ${knowledgeEntry.title}`,
      media_filename: knowledgeEntry.image_filename,
      media_url: knowledgeEntry.image_url,
      media_type: 'image',
      mime_type: knowledgeEntry.image_mime_type,
    });

    // Update usage statistics
    await Promise.all([
      supabase.rpc("increment_message_usage", {
        p_auth_id: authId,
        p_message_type: "outgoing",
      }),
      // Track outgoing message analytics
      updateAnalytics(authId, phoneNumber, {
        messageType: 'outgoing',
        knowledgeQuery: false,
        knowledgeHit: false,
        knowledgeSectionsFound: 0,
        similarityScore: 0,
        responseTime: 0,
        totalTokens: 0,
        promptTokens: 0,
        completionTokens: 0
      })
    ]);

    res.json({
      success: true,
      message: "Knowledge base image sent successfully",
      data: {
        knowledgeEntry: {
          id: knowledgeEntry.id,
          title: knowledgeEntry.title,
        },
        message: messageResult,
      },
    });
  } catch (error) {
    console.error("Error sending knowledge base image:", error);
    res.status(500).json({ error: "Failed to send knowledge base image" });
  }
});

router.get("/list-knowledge", async (req, res) => {
  try {
    const {
      authId,
      search,
      sortBy = "created_at",
      sortOrder = "desc",
    } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    // Get pagination parameters
    const { limit, page, offset } = getPaginationParams(req.query);

    // Build query with filters
    let query = supabase
      .from("knowledge_base")
      .select("id, title, content, created_at, updated_at, has_image, image_filename, image_url, image_mime_type, image_file_size", { count: "exact" })
      .eq("auth_id", authId);

    // Apply search filter
    if (search) {
      query = query.or(`title.ilike.%${search}%, content.ilike.%${search}%`);
    }

    // Apply sorting
    const validSortFields = ["created_at", "updated_at", "title"];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "created_at";
    const ascending = sortOrder === "asc";

    query = query.order(sortField, { ascending });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) throw error;

    res.json(buildPaginatedResponse(data, count || 0, page, limit));
  } catch (error) {
    console.error("Error listing knowledge:", error);
    res.status(500).json({ error: "Failed to list knowledge base entries" });
  }
});

// Get chatbot settings for a user
router.get("/chatbot-settings", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    // Get chatbot settings for the user
    let { data, error } = await supabase
      .from("chatbot_settings")
      .select("*")
      .eq("auth_id", authId)
      .single();

    if (error && error.code !== "PGRST116") {
      // PGRST116 = no rows returned
      throw error;
    }

    // If no settings found, create default settings
    if (!data) {
      const { data: newSettings, error: insertError } = await supabase
        .from("chatbot_settings")
        .insert([{ auth_id: authId }])
        .select()
        .single();

      if (insertError) throw insertError;
      data = newSettings;
    }

    res.json({
      success: true,
      data: data,
    });
  } catch (error) {
    console.error("Error getting chatbot settings:", error);
    res.status(500).json({ error: "Failed to get chatbot settings" });
  }
});

// Update chatbot settings for a user
router.put("/chatbot-settings", async (req, res) => {
  try {
    const {
      authId,
      systemPrompt,
      orderSystemPrompt,
      model,
      temperature,
      maxTokens,
      chatHistoryLimit,
      similarityThreshold,
      matchCount,
      orderProcessingEnabled,
    } = req.body;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required field: authId",
      });
    }

    // Prepare update data (only include fields that are provided)
    const updateData = {};
    if (systemPrompt !== undefined) updateData.system_prompt = systemPrompt;
    if (orderSystemPrompt !== undefined) updateData.order_system_prompt = orderSystemPrompt;
    if (model !== undefined) updateData.model = model;
    if (temperature !== undefined) updateData.temperature = temperature;
    if (maxTokens !== undefined) updateData.max_tokens = maxTokens;
    if (chatHistoryLimit !== undefined)
      updateData.chat_history_limit = chatHistoryLimit;
    if (similarityThreshold !== undefined)
      updateData.similarity_threshold = similarityThreshold;
    if (matchCount !== undefined) updateData.match_count = matchCount;
    if (orderProcessingEnabled !== undefined) {
      // Check if user has a paid plan for order processing
      const { data: quotaCheck, error: quotaError } = await supabase.rpc(
        "check_user_quotas",
        { p_auth_id: authId },
      );

      if (quotaError) {
        return res.status(500).json({ error: "Failed to check user quota" });
      }

      const quotaInfo = quotaCheck[0];
      const isPaidPlan = quotaInfo && quotaInfo.plan_name !== 'free' && quotaInfo.plan_name !== 'none';

      if (orderProcessingEnabled && !isPaidPlan) {
        return res.status(403).json({
          error: "Order processing is only available for paid plans. Please upgrade your subscription.",
        });
      }

      updateData.order_processing_enabled = orderProcessingEnabled;
    }

    // First, try to update existing settings
    const { data: updatedData, error: updateError } = await supabase
      .from("chatbot_settings")
      .update(updateData)
      .eq("auth_id", authId)
      .select()
      .single();

    if (updateError && updateError.code === "PGRST116") {
      // No existing settings, create new ones
      const { data: newData, error: insertError } = await supabase
        .from("chatbot_settings")
        .insert([{ auth_id: authId, ...updateData }])
        .select()
        .single();

      if (insertError) throw insertError;

      return res.json({
        success: true,
        message: "Chatbot settings created successfully",
        data: newData,
      });
    }

    if (updateError) throw updateError;

    res.json({
      success: true,
      message: "Chatbot settings updated successfully",
      data: updatedData,
    });
  } catch (error) {
    console.error("Error updating chatbot settings:", error);
    console.error("Error details:", {
      message: error.message,
      code: error.code,
      details: error.details,
      hint: error.hint
    });
    res.status(500).json({
      error: "Failed to update chatbot settings",
      details: error.message
    });
  }
});

// Get contacts for a user
router.get("/contacts", async (req, res) => {
  try {
    const {
      authId,
      search,
      isActive,
      sortBy = "last_message_at",
      sortOrder = "desc",
    } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    // Get pagination parameters
    const { limit, page, offset } = getPaginationParams(req.query);

    // Build query with filters
    let query = supabase
      .from("contacts")
      .select("*", { count: "exact" })
      .eq("auth_id", authId);

    // Apply filters
    if (search) {
      query = query.or(
        `name.ilike.%${search}%, phone_number.ilike.%${search}%`,
      );
    }

    if (isActive !== undefined) {
      query = query.eq("is_active", isActive === "true");
    }

    // Apply sorting
    const validSortFields = [
      "last_message_at",
      "first_message_at",
      "name",
      "total_messages",
      "created_at",
    ];
    const sortField = validSortFields.includes(sortBy)
      ? sortBy
      : "last_message_at";
    const ascending = sortOrder === "asc";

    query = query.order(sortField, { ascending });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) throw error;

    res.json(buildPaginatedResponse(data, count || 0, page, limit));
  } catch (error) {
    console.error("Error getting contacts:", error);
    res.status(500).json({ error: "Failed to get contacts" });
  }
});

// Update contact information
router.put("/contacts/:contactId", async (req, res) => {
  try {
    const { contactId } = req.params;
    const { authId, name, tags, notes, isActive } = req.body;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required field: authId",
      });
    }

    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (tags !== undefined) updateData.tags = tags;
    if (notes !== undefined) updateData.notes = notes;
    if (isActive !== undefined) updateData.is_active = isActive;

    const { data, error } = await supabase
      .from("contacts")
      .update(updateData)
      .eq("id", contactId)
      .eq("auth_id", authId)
      .select()
      .single();

    if (error) throw error;

    res.json({
      success: true,
      message: "Contact updated successfully",
      data: data,
    });
  } catch (error) {
    console.error("Error updating contact:", error);
    res.status(500).json({ error: "Failed to update contact" });
  }
});

// Get daily statistics
router.get("/statistics/daily", async (req, res) => {
  try {
    const { authId, startDate, endDate, limit = 30 } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    let query = supabase
      .from("daily_statistics")
      .select("*")
      .eq("auth_id", authId)
      .order("date", { ascending: false })
      .limit(limit);

    if (startDate) {
      query = query.gte("date", startDate);
    }
    if (endDate) {
      query = query.lte("date", endDate);
    }

    const { data, error } = await query;

    if (error) throw error;

    res.json({
      success: true,
      data: data,
      count: data.length,
    });
  } catch (error) {
    console.error("Error getting daily statistics:", error);
    res.status(500).json({ error: "Failed to get daily statistics" });
  }
});

// Get monthly statistics
router.get("/statistics/monthly", async (req, res) => {
  try {
    const { authId, year, limit = 12 } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    let query = supabase
      .from("monthly_statistics")
      .select("*")
      .eq("auth_id", authId)
      .order("year", { ascending: false })
      .order("month", { ascending: false })
      .limit(limit);

    if (year) {
      query = query.eq("year", year);
    }

    const { data, error } = await query;

    if (error) throw error;

    res.json({
      success: true,
      data: data,
      count: data.length,
    });
  } catch (error) {
    console.error("Error getting monthly statistics:", error);
    res.status(500).json({ error: "Failed to get monthly statistics" });
  }
});

// Get real-time statistics overview
router.get("/statistics/overview", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    // Get current month statistics
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;

    const { data: monthlyStats, error: monthlyError } = await supabase
      .from("monthly_statistics")
      .select("*")
      .eq("auth_id", authId)
      .eq("year", currentYear)
      .eq("month", currentMonth)
      .single();

    // Get today's statistics
    const today = new Date().toISOString().split("T")[0];
    const { data: todayStats, error: todayError } = await supabase
      .from("daily_statistics")
      .select("*")
      .eq("auth_id", authId)
      .eq("date", today)
      .single();

    // Get total contacts
    const { count: totalContacts, error: contactsError } = await supabase
      .from("contacts")
      .select("*", { count: "exact", head: true })
      .eq("auth_id", authId);

    // Get active contacts (messaged in last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const { count: activeContacts, error: activeError } = await supabase
      .from("contacts")
      .select("*", { count: "exact", head: true })
      .eq("auth_id", authId)
      .gte("last_message_at", sevenDaysAgo.toISOString());

    // Get recent message statistics (last 7 days)
    const { data: recentStats, error: recentError } = await supabase
      .from("message_statistics")
      .select("total_tokens, response_time_ms, knowledge_sections_found")
      .eq("auth_id", authId)
      .eq("message_type", "outgoing")
      .gte("created_at", sevenDaysAgo.toISOString());

    const overview = {
      today: todayStats || {
        total_messages: 0,
        incoming_messages: 0,
        outgoing_messages: 0,
        unique_contacts: 0,
        total_tokens: 0,
        avg_response_time_ms: 0,
      },
      thisMonth: monthlyStats || {
        total_messages: 0,
        incoming_messages: 0,
        outgoing_messages: 0,
        unique_contacts: 0,
        new_contacts: 0,
        total_tokens: 0,
        total_cost_usd: 0,
      },
      contacts: {
        total: totalContacts || 0,
        active: activeContacts || 0,
      },
      performance: {
        avgResponseTime:
          recentStats && recentStats.length > 0
            ? Math.round(
                recentStats.reduce(
                  (sum, stat) => sum + (stat.response_time_ms || 0),
                  0,
                ) / recentStats.length,
              )
            : 0,
        avgTokensPerMessage:
          recentStats && recentStats.length > 0
            ? Math.round(
                recentStats.reduce(
                  (sum, stat) => sum + (stat.total_tokens || 0),
                  0,
                ) / recentStats.length,
              )
            : 0,
        knowledgeHitRate:
          recentStats && recentStats.length > 0
            ? Math.round(
                (recentStats.filter((stat) => stat.knowledge_sections_found > 0)
                  .length /
                  recentStats.length) *
                  100,
              )
            : 0,
      },
    };

    res.json({
      success: true,
      data: overview,
    });
  } catch (error) {
    console.error("Error getting statistics overview:", error);
    res.status(500).json({ error: "Failed to get statistics overview" });
  }
});

// Get message statistics with filters
router.get("/statistics/messages", async (req, res) => {
  try {
    const {
      authId,
      phoneNumber,
      messageType,
      startDate,
      endDate,
      sortBy = "created_at",
      sortOrder = "desc",
    } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    // Get pagination parameters
    const { limit, page, offset } = getPaginationParams(req.query);

    let query = supabase
      .from("message_statistics")
      .select("*", { count: "exact" })
      .eq("auth_id", authId);

    // Apply filters
    if (phoneNumber) query = query.eq("phone_number", phoneNumber);
    if (messageType) query = query.eq("message_type", messageType);
    if (startDate) query = query.gte("created_at", startDate);
    if (endDate) query = query.lte("created_at", endDate);

    // Apply sorting
    const validSortFields = [
      "created_at",
      "response_time_ms",
      "total_tokens",
      "similarity_score",
    ];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "created_at";
    const ascending = sortOrder === "asc";

    query = query.order(sortField, { ascending });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) throw error;

    res.json(buildPaginatedResponse(data, count || 0, page, limit));
  } catch (error) {
    console.error("Error getting message statistics:", error);
    res.status(500).json({ error: "Failed to get message statistics" });
  }
});

// Get chat history for a specific contact
router.get("/chat-history", async (req, res) => {
  try {
    const { authId, phoneNumber, limit = 50, offset = 0, includeMedia = true } = req.query;

    if (!authId || !phoneNumber) {
      return res.status(400).json({
        error: "Missing required parameters: authId and phoneNumber",
      });
    }

    // Clean and normalize phone number (handle URL encoding issues)
    let cleanPhoneNumber = phoneNumber.trim();
    // Replace spaces with + (in case + got URL decoded to space)
    if (cleanPhoneNumber.startsWith(" ")) {
      cleanPhoneNumber = "+" + cleanPhoneNumber.substring(1);
    }

    // Try multiple phone number formats for matching
    const phoneVariations = [
      cleanPhoneNumber,
      phoneNumber, // original
      phoneNumber.replace(/\s/g, "+"), // replace spaces with +
      phoneNumber.replace(/^\s/, "+"), // replace leading space with +
      cleanPhoneNumber.replace(/[^\d+]/g, ""), // remove all non-digits except +
    ];

    let data = null;
    let count = 0;

    // Try each phone number variation
    for (const phoneVar of phoneVariations) {
      const { data: tryData, error: tryError } = await supabase
        .from("chat_history")
        .select("id, role, content, created_at, phone_number, media_filename, media_url, media_type, mime_type, file_size")
        .eq("auth_id", authId)
        .eq("phone_number", phoneVar)
        .order("created_at", { ascending: false }) // Get latest messages first
        .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

      if (!tryError && tryData && tryData.length > 0) {
        data = tryData;

        // Get count for this variation
        const { count: tryCount } = await supabase
          .from("chat_history")
          .select("*", { count: "exact", head: true })
          .eq("auth_id", authId)
          .eq("phone_number", phoneVar);

          count = tryCount;
        break;
      }
    }

    // Get media files for this conversation if requested
    let mediaFiles = {};
    if (includeMedia === 'true' || includeMedia === true) {
      try {
        const { data: mediaData, error: mediaError } = await supabase
          .from("uploaded_media")
          .select("*")
          .eq("auth_id", authId)
          .eq("phone_number", phoneNumber)
          .eq("source", "whatsapp_incoming")
          .order("uploaded_at", { ascending: false });

        if (!mediaError && mediaData) {
          // Group media by approximate timestamp to match with messages
          mediaData.forEach(media => {
            const mediaTimestamp = new Date(media.uploaded_at).getTime();
            mediaFiles[mediaTimestamp] = media;
          });
        }
      } catch (mediaError) {
        console.error("Error fetching media files:", mediaError);
      }
    }

    // Format the response to make it clearer and include media info
    const formattedData = (data || []).map((message) => {
      try {
        let mediaInfo = null;
        
        // First check if media info is stored directly in chat_history
        if (includeMedia && message.media_filename) {
          mediaInfo = {
            filename: message.media_filename,
            fileUrl: message.media_url ? 
              (message.media_url.startsWith('http') ? message.media_url : `${req.protocol}://${req.get('host')}${message.media_url}`) : 
              `${req.protocol}://${req.get('host')}/api/ai/media/${message.media_filename}`,
            mediaType: message.media_type,
            mimeType: message.mime_type,
            fileSize: message.file_size,
            originalName: message.media_filename
          };
        }

        // Fallback: Check if this message might have associated media in uploaded_media table
        else if (includeMedia && (message.content.includes('[Customer sent an image]') || 
                                 message.content.includes('[Customer sent a voice message]') ||
                                 message.content.includes('[Customer sent a video]'))) {
          
          const messageTimestamp = new Date(message.created_at).getTime();

          // Find media file within 30 seconds of the message timestamp
          const tolerance = 30000; // 30 seconds
          const matchingMedia = Object.entries(mediaFiles).find(([timestamp, media]) => {
            const timeDiff = Math.abs(parseInt(timestamp) - messageTimestamp);
            return timeDiff <= tolerance;
          });
          
          if (matchingMedia) {
            const media = matchingMedia[1];
            mediaInfo = {
              filename: media.filename,
              fileUrl: media.file_url ? 
                (media.file_url.startsWith('http') ? media.file_url : `${req.protocol}://${req.get('host')}${media.file_url}`) : 
                `${req.protocol}://${req.get('host')}/api/ai/media/${media.filename}`,
              mediaType: media.media_type,
              mimeType: media.mime_type,
              fileSize: media.file_size,
              originalName: media.original_name
            };
          }
        }

        const formattedMessage = {
          id: message.id,
          sender: message.role === "user" ? "Customer" : "Assistant",
          role: message.role,
          content: message.content,
          timestamp: message.created_at,
          phoneNumber: message.phone_number,
          mediaInfo: mediaInfo,
          hasMedia: !!mediaInfo
        };

        return formattedMessage;
      } catch (error) {
        console.error('Error formatting message:', error, message);
        // Return a basic version of the message if there's an error
        return {
          id: message.id || 'unknown',
          sender: message.role === "user" ? "Customer" : "Assistant",
          role: message.role || 'unknown',
          content: message.content || '',
          timestamp: message.created_at || new Date().toISOString(),
          phoneNumber: message.phone_number || '',
          hasMedia: false
        };
      }
    });

    res.json({
      success: true,
      data: formattedData,
      pagination: {
        total: count || 0,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: parseInt(offset) + parseInt(limit) < (count || 0),
      },
      mediaCount: Object.keys(mediaFiles).length,
    });
  } catch (error) {
    console.error("Error getting chat history:", error);
    res.status(500).json({ error: "Failed to get chat history" });
  }
});

// Get media file for a specific chat message
router.get("/chat-media/:messageId", async (req, res) => {
  try {
    const { messageId } = req.params;
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    // Get message with media information
    const { data: message, error } = await supabase
      .from("chat_history")
      .select("id, role, content, created_at, phone_number, media_filename, media_url, media_type, mime_type, file_size")
      .eq("id", messageId)
      .eq("auth_id", authId)
      .single();

    if (error || !message) {
      return res.status(404).json({
        error: "Message not found or you don't have permission to access it",
      });
    }

    if (!message.media_filename) {
      return res.status(404).json({
        error: "No media file associated with this message",
      });
    }

    const mediaInfo = {
      messageId: message.id,
      filename: message.media_filename,
      fileUrl: message.media_url ? 
        (message.media_url.startsWith('http') ? message.media_url : `${req.protocol}://${req.get('host')}${message.media_url}`) : 
        `${req.protocol}://${req.get('host')}/api/ai/media/${message.media_filename}`,
      mediaType: message.media_type,
      mimeType: message.mime_type,
      fileSize: message.file_size,
      messageContent: message.content,
      timestamp: message.created_at
    };

    res.json({
      success: true,
      data: mediaInfo,
    });

  } catch (error) {
    console.error("Error getting chat media:", error);
    res.status(500).json({ error: "Failed to get chat media" });
  }
});

// Get frontend implementation examples for displaying media in chat
router.get("/chat-media-examples", async (req, res) => {
  try {
    const baseUrl = `${req.protocol}://${req.get('host')}/api/ai`;
    
    const examples = {
      title: "Chat History with Media Display Examples",
      description: "Frontend implementation examples for displaying images, videos, and audio in chat history",
      
      api_usage: {
        get_chat_history: {
          url: `${baseUrl}/chat-history?authId=USER_ID&phoneNumber=%2B1234567890&includeMedia=true`,
          description: "Get chat history with media information included"
        },
        response_format: {
          success: true,
          data: [
            {
              id: 123,
              sender: "Customer",
              role: "user",
              content: "[Customer sent an image] A red car parked in front of a house",
              timestamp: "2024-01-15T10:30:00Z",
              phoneNumber: "+1234567890",
              hasMedia: true,
              mediaInfo: {
                filename: "whatsapp-image-1641234567890-123456789.jpg",
                fileUrl: `${baseUrl}/media/whatsapp-image-1641234567890-123456789.jpg`,
                mediaType: "image",
                mimeType: "image/jpeg",
                fileSize: 245760,
                originalName: "whatsapp-image.jpg"
              }
            }
          ]
        }
      },

      frontend_examples: {
        react_component: `
import React from 'react';

const ChatMessage = ({ message }) => {
  const renderMedia = () => {
    if (!message.hasMedia || !message.mediaInfo) return null;
    
    const { mediaType, fileUrl, mimeType } = message.mediaInfo;
    
    switch (mediaType) {
      case 'image':
        return (
          <div className="media-container">
            <img 
              src={fileUrl} 
              alt="Customer sent image"
              className="chat-image"
              style={{ maxWidth: '300px', borderRadius: '8px' }}
              onClick={() => window.open(fileUrl, '_blank')}
            />
          </div>
        );
        
      case 'video':
        return (
          <div className="media-container">
            <video 
              controls 
              className="chat-video"
              style={{ maxWidth: '300px', borderRadius: '8px' }}
            >
              <source src={fileUrl} type={mimeType} />
              Your browser does not support the video tag.
            </video>
          </div>
        );
        
      case 'audio':
        return (
          <div className="media-container">
            <audio controls className="chat-audio">
              <source src={fileUrl} type={mimeType} />
              Your browser does not support the audio tag.
            </audio>
          </div>
        );
        
      default:
        return (
          <div className="media-container">
            <a href={fileUrl} target="_blank" rel="noopener noreferrer">
              📎 {message.mediaInfo.originalName || 'Media file'}
            </a>
          </div>
        );
    }
  };

  return (
    <div className={\`message \${message.sender.toLowerCase()}\`}>
      <div className="message-header">
        <span className="sender">{message.sender}</span>
        <span className="timestamp">{new Date(message.timestamp).toLocaleString()}</span>
      </div>
      
      {message.hasMedia && renderMedia()}
      
      <div className="message-content">
        {message.content}
      </div>
    </div>
  );
};

const ChatHistory = ({ authId, phoneNumber }) => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchChatHistory = async () => {
      try {
        const response = await fetch(
          \`/api/ai/chat-history?authId=\${authId}&phoneNumber=\${encodeURIComponent(phoneNumber)}&includeMedia=true\`
        );
        const result = await response.json();
        
        if (result.success) {
          setMessages(result.data.reverse()); // Reverse to show oldest first
        }
      } catch (error) {
        console.error('Error fetching chat history:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchChatHistory();
  }, [authId, phoneNumber]);

  if (loading) return <div>Loading chat history...</div>;

  return (
    <div className="chat-history">
      {messages.map(message => (
        <ChatMessage key={message.id} message={message} />
      ))}
    </div>
  );
};
        `,
        
        vanilla_javascript: `
// Fetch and display chat history with media
async function loadChatHistory(authId, phoneNumber, containerId) {
  try {
    const response = await fetch(
      \`/api/ai/chat-history?authId=\${authId}&phoneNumber=\${encodeURIComponent(phoneNumber)}&includeMedia=true\`
    );
    const result = await response.json();
    
    if (result.success) {
      const container = document.getElementById(containerId);
      container.innerHTML = '';
      
      result.data.reverse().forEach(message => {
        const messageEl = createMessageElement(message);
        container.appendChild(messageEl);
      });
    }
  } catch (error) {
    console.error('Error loading chat history:', error);
  }
}

function createMessageElement(message) {
  const messageDiv = document.createElement('div');
  messageDiv.className = \`message \${message.sender.toLowerCase()}\`;
  
  // Message header
  const headerDiv = document.createElement('div');
  headerDiv.className = 'message-header';
  headerDiv.innerHTML = \`
    <span class="sender">\${message.sender}</span>
    <span class="timestamp">\${new Date(message.timestamp).toLocaleString()}</span>
  \`;
  messageDiv.appendChild(headerDiv);
  
  // Media content
  if (message.hasMedia && message.mediaInfo) {
    const mediaEl = createMediaElement(message.mediaInfo);
    if (mediaEl) messageDiv.appendChild(mediaEl);
  }
  
  // Text content
  const contentDiv = document.createElement('div');
  contentDiv.className = 'message-content';
  contentDiv.textContent = message.content;
  messageDiv.appendChild(contentDiv);
  
  return messageDiv;
}

function createMediaElement(mediaInfo) {
  const mediaContainer = document.createElement('div');
  mediaContainer.className = 'media-container';
  
  switch (mediaInfo.mediaType) {
    case 'image':
      const img = document.createElement('img');
      img.src = mediaInfo.fileUrl;
      img.alt = 'Customer sent image';
      img.style.maxWidth = '300px';
      img.style.borderRadius = '8px';
      img.style.cursor = 'pointer';
      img.onclick = () => window.open(mediaInfo.fileUrl, '_blank');
      mediaContainer.appendChild(img);
      break;
      
    case 'video':
      const video = document.createElement('video');
      video.controls = true;
      video.style.maxWidth = '300px';
      video.style.borderRadius = '8px';
      
      const videoSource = document.createElement('source');
      videoSource.src = mediaInfo.fileUrl;
      videoSource.type = mediaInfo.mimeType;
      video.appendChild(videoSource);
      
      mediaContainer.appendChild(video);
      break;
      
    case 'audio':
      const audio = document.createElement('audio');
      audio.controls = true;
      
      const audioSource = document.createElement('source');
      audioSource.src = mediaInfo.fileUrl;
      audioSource.type = mediaInfo.mimeType;
      audio.appendChild(audioSource);
      
      mediaContainer.appendChild(audio);
      break;
      
    default:
      const link = document.createElement('a');
      link.href = mediaInfo.fileUrl;
      link.target = '_blank';
      link.rel = 'noopener noreferrer';
      link.textContent = \`📎 \${mediaInfo.originalName || 'Media file'}\`;
      mediaContainer.appendChild(link);
  }
  
  return mediaContainer;
}
        `,
        
        css_styles: `
.chat-history {
  max-height: 600px;
  overflow-y: auto;
  padding: 20px;
  background: #f5f5f5;
}

.message {
  margin-bottom: 15px;
  padding: 12px;
  border-radius: 12px;
  max-width: 70%;
}

.message.customer {
  background: #e3f2fd;
  margin-left: auto;
  margin-right: 0;
}

.message.assistant {
  background: #ffffff;
  margin-left: 0;
  margin-right: auto;
  border: 1px solid #e0e0e0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.sender {
  font-weight: bold;
}

.timestamp {
  color: #999;
}

.media-container {
  margin: 10px 0;
  text-align: center;
}

.chat-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: transform 0.2s;
}

.chat-image:hover {
  transform: scale(1.02);
}

.chat-video {
  max-width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chat-audio {
  width: 100%;
  margin: 8px 0;
}

.message-content {
  font-size: 14px;
  line-height: 1.4;
  color: #333;
}
        `
      },
      
      notes: {
        performance: "Media files are served directly from your server for fast loading",
        security: "Media access is controlled by authId validation",
        storage: "Media files are stored locally and referenced in the database",
        formats: "Supports all major image, video, and audio formats",
        mobile: "All media elements are responsive and mobile-friendly"
      }
    };

    res.json(examples);
  } catch (error) {
    console.error("Error generating media examples:", error);
    res.status(500).json({ error: "Failed to generate examples" });
  }
});

// ===== SUBSCRIPTION & QUOTA ROUTES =====

// Get user's current subscription and quota status
router.get("/subscription-status", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({ error: "Missing authId parameter" });
    }

    // Get subscription details
    const { data: subscription, error: subError } = await supabase.rpc(
      "get_current_user_subscription",
      { p_auth_id: authId },
    );

    if (subError) {
      console.error("Error getting subscription:", subError);
      return res
        .status(500)
        .json({ error: "Failed to get subscription details" });
    }

    // Get quota information
    const { data: quotaInfo, error: quotaError } = await supabase.rpc(
      "check_user_quotas",
      { p_auth_id: authId },
    );

    if (quotaError) {
      console.error("Error checking quotas:", quotaError);
      return res.status(500).json({ error: "Failed to check quotas" });
    }

    // Get current month usage
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();

    const { data: monthlyUsage, error: usageError } = await supabase
      .from("monthly_usage")
      .select("*")
      .eq("auth_id", authId)
      .eq("year", currentYear)
      .eq("month", currentMonth)
      .single();

    if (usageError && usageError.code !== "PGRST116") {
      console.error("Error getting usage:", usageError);
    }

    const subscriptionData = subscription[0] || null;
    const quotaData = quotaInfo[0] || null;

    res.json({
      success: true,
      data: {
        subscription: subscriptionData,
        quotas: quotaData,
        usage: monthlyUsage || {
          messages_sent: 0,
          messages_received: 0,
          total_contacts: 0,
          knowledge_base_queries: 0,
        },
        limits: {
          messagesRemaining:
            quotaData?.message_limit === -1
              ? "unlimited"
              : Math.max(
                  0,
                  (quotaData?.message_limit || 0) -
                    (quotaData?.current_message_count || 0),
                ),
          contactsRemaining:
            quotaData?.contact_limit === -1
              ? "unlimited"
              : Math.max(
                  0,
                  (quotaData?.contact_limit || 0) -
                    (quotaData?.current_contact_count || 0),
                ),
        },
      },
    });
  } catch (error) {
    console.error("Error getting subscription status:", error);
    res.status(500).json({ error: "Failed to get subscription status" });
  }
});

// Get available subscription plans
router.get("/plans", async (req, res) => {
  try {
    const { data: plans, error } = await supabase
      .from("subscription_plans")
      .select("*")
      .eq("is_active", true)
      .order("price", { ascending: true });

    if (error) throw error;

    res.json({
      success: true,
      data: plans,
    });
  } catch (error) {
    console.error("Error getting plans:", error);
    res.status(500).json({ error: "Failed to get subscription plans" });
  }
});

// Check user quota status - simplified endpoint for quick quota checks
router.get("/quota-status", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({ error: "Missing authId parameter" });
    }

    // Get user quota information
    const { data: quotaData, error: quotaError } = await supabase.rpc(
      "check_user_quotas",
      {
        p_auth_id: authId,
      },
    );

    if (quotaError) throw quotaError;

    const quotaStatus = quotaData[0];

    if (!quotaStatus) {
      return res.status(404).json({ error: "User not found" });
    }

    // Get current month's usage
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    const { data: usageData, error: usageError } = await supabase
      .from("monthly_usage")
      .select("*")
      .eq("auth_id", authId)
      .eq("year", currentYear)
      .eq("month", currentMonth)
      .single();

    // If no usage data exists, create default values
    const usage = usageData || {
      messages_sent: 0,
      messages_received: 0,
      total_contacts: 0, // will be replaced below
      new_contacts: 0,
      knowledge_base_queries: 0,
    };

    // Get all-time total contacts from contacts table
    const { count: allTimeTotalContacts, error: contactsCountError } = await supabase
      .from("contacts")
      .select("*", { count: "exact", head: true })
      .eq("auth_id", authId);

    // Calculate totals and remaining quotas
    const messageLimit = quotaStatus.message_limit;
    const contactLimit = quotaStatus.contact_limit;
    const totalMessages = usage.messages_sent + usage.messages_received;

    const remainingMessages =
      messageLimit === -1 ? -1 : Math.max(0, messageLimit - totalMessages);
    const remainingContacts =
      contactLimit === -1
        ? -1
        : Math.max(0, contactLimit - (allTimeTotalContacts || 0));

    // Calculate usage percentages
    const messageUsagePercent =
      messageLimit === -1
        ? 0
        : Math.min(100, (totalMessages / messageLimit) * 100);
    const contactUsagePercent =
      contactLimit === -1
        ? 0
        : Math.min(100, ((allTimeTotalContacts || 0) / contactLimit) * 100);

    // Determine quota status flags
    const isMessageLimitExceeded =
      messageLimit !== -1 && totalMessages >= messageLimit;
    const isContactLimitExceeded =
      contactLimit !== -1 && (allTimeTotalContacts || 0) >= contactLimit;
    const isSubscriptionExpired = quotaStatus.subscription_status === "expired";

    // Warning thresholds (80% and 90%)
    const messageWarning80 = messageLimit !== -1 && messageUsagePercent >= 80;
    const messageWarning90 = messageLimit !== -1 && messageUsagePercent >= 90;
    const contactWarning80 = contactLimit !== -1 && contactUsagePercent >= 80;
    const contactWarning90 = contactLimit !== -1 && contactUsagePercent >= 90;

    res.json({
      success: true,
      quota: {
        // Basic info
        plan: quotaStatus.plan_name,
        subscriptionStatus: quotaStatus.subscription_status,
        subscriptionEndDate: quotaStatus.subscription_end_date,

        // Limits (-1 means unlimited)
        limits: {
          monthlyMessages: messageLimit,
          totalContacts: contactLimit,
          knowledgeBase: quotaStatus.knowledge_base_limit || -1,
          productCatalog: quotaStatus.product_catalog_limit || -1,
        },

        // Current usage
        usage: {
          totalMessages: totalMessages,
          messagesSent: usage.messages_sent,
          messagesReceived: usage.messages_received,
          totalContacts: allTimeTotalContacts || 0,
          newContactsThisMonth: usage.new_contacts,
          knowledgeBaseQueries: usage.knowledge_base_queries,
        },

        // Remaining quotas
        remaining: {
          messages: remainingMessages,
          contacts: remainingContacts,
        },

        // Usage percentages (0-100)
        usagePercent: {
          messages: Math.round(messageUsagePercent),
          contacts: Math.round(contactUsagePercent),
        },

        // Status flags
        status: {
          canSendMessages: !isMessageLimitExceeded && !isSubscriptionExpired,
          canAddContacts: !isContactLimitExceeded && !isSubscriptionExpired,
          isMessageLimitExceeded: isMessageLimitExceeded,
          isContactLimitExceeded: isContactLimitExceeded,
          isSubscriptionExpired: isSubscriptionExpired,
          hasUnlimitedMessages: messageLimit === -1,
          hasUnlimitedContacts: contactLimit === -1,
        },

        // Warning flags for UI alerts
        warnings: {
          messageUsage80: messageWarning80,
          messageUsage90: messageWarning90,
          contactUsage80: contactWarning80,
          contactUsage90: contactWarning90,
          subscriptionExpiring:
            quotaStatus.days_until_expiry <= 7 &&
            quotaStatus.days_until_expiry > 0,
        },

        // Current period info
        period: {
          year: currentYear,
          month: currentMonth,
          monthName: currentDate.toLocaleString("default", { month: "long" }),
          daysUntilExpiry: quotaStatus.days_until_expiry,
        },
      },
    });
  } catch (error) {
    console.error("Error checking user quota:", error);
    res.status(500).json({ error: "Failed to check quota status" });
  }
});

// ===== WHATSAPP ROUTES =====

// Verify webhook (required by Meta)
router.get("/webhook", (req, res) => {
  const mode = req.query["hub.mode"];
  const token = req.query["hub.verify_token"];
  const challenge = req.query["hub.challenge"];

  // Check if a token and mode were sent
  if (mode && token) {
    // Check the mode and token sent are correct
    if (mode === "subscribe" && token === process.env.VERIFY_TOKEN) {
      // Respond with 200 OK and challenge token from the request
      res.status(200).send(challenge);
    } else {
      // Response with '403 Forbidden' if tokens don't match
      res.sendStatus(403);
    }
  }
});

/**
 * WhatsApp Webhook Handler
 * 
 * Supports multiple message types:
 * - Text messages: Processed directly
 * - Images: Analyzed using OpenAI GPT-4 Vision API
 * - Voice/Audio: Transcribed using OpenAI Whisper API
 * - Other types: Ignored (documents, locations, stickers, etc.)
 * 
 * Media Processing Flow:
 * 1. Download media from WhatsApp using Media API
 * 2. Process with appropriate OpenAI service
 * 3. Convert to text description/transcription
 * 4. Continue with normal AI chatbot flow
 */
router.post("/webhook", async (req, res) => {
  try {
    const body = req.body;

    // Check if this is a WhatsApp Business Account notification
    if (body.object) {
      if (
        body.entry &&
        body.entry[0].changes &&
        body.entry[0].changes[0] &&
        body.entry[0].changes[0].value.messages &&
        body.entry[0].changes[0].value.messages[0]
      ) {
        const phoneNumberId =
          body.entry[0].changes[0].value.metadata.phone_number_id;
        const message = body.entry[0].changes[0].value.messages[0];
        const fromNumber = message.from;
        const msgId = message.id;

        // Debug: Log raw message for document attachment debugging
        if (message.document || message.image || message.audio || message.video) {
          console.log(`📱 Raw media message received from ${fromNumber}:`, JSON.stringify(message, null, 2));
        }

        // Ignore messages from specific number
        if (fromNumber === '+***********' || fromNumber === '***********') {
          logger.debug(`Ignoring message from blocked number: ${fromNumber}`);
          res.status(200).send("EVENT_RECEIVED");
          return;
        }

        // Determine message type and extract content
        let messageContent = null;
        let messageType = 'unsupported';

        // Debug: Log the complete message structure
        console.log(`📱 Incoming message structure:`, {
          hasText: !!message.text,
          hasImage: !!message.image,
          hasAudio: !!message.audio,
          hasVideo: !!message.video,
          hasDocument: !!message.document,
          messageKeys: Object.keys(message).filter(key => !['from', 'id', 'timestamp'].includes(key))
        });

        if (message.text) {
          messageType = 'text';
          messageContent = message.text.body;
        } else if (message.image) {
          messageType = 'image';
          messageContent = message.image;
        } else if (message.audio) {
          messageType = 'audio';
          messageContent = message.audio;
        } else if (message.video) {
          messageType = 'video';
          messageContent = message.video;
        } else if (message.document) {
          // Handle document attachments - check mime type to determine if it's an image, audio, or video
          const mimeType = message.document.mime_type || '';

          console.log(`📎 Document attachment received:`, {
            mime_type: mimeType,
            filename: message.document.filename,
            id: message.document.id,
            sha256: message.document.sha256,
            file_size: message.document.file_size
          });

          if (mimeType.startsWith('image/')) {
            messageType = 'image';
            messageContent = message.document;
            console.log(`✅ Image attachment detected | MIME: ${mimeType} | Filename: ${message.document.filename || 'unknown'}`);
            logger.debug(`📎 Image attachment detected | MIME: ${mimeType} | Filename: ${message.document.filename || 'unknown'}`);
          } else if (mimeType.startsWith('audio/')) {
            messageType = 'audio';
            messageContent = message.document;
            console.log(`✅ Audio attachment detected | MIME: ${mimeType} | Filename: ${message.document.filename || 'unknown'}`);
            logger.debug(`📎 Audio attachment detected | MIME: ${mimeType} | Filename: ${message.document.filename || 'unknown'}`);
          } else if (mimeType.startsWith('video/')) {
            messageType = 'video';
            messageContent = message.document;
            console.log(`✅ Video attachment detected | MIME: ${mimeType} | Filename: ${message.document.filename || 'unknown'}`);
            logger.debug(`📎 Video attachment detected | MIME: ${mimeType} | Filename: ${message.document.filename || 'unknown'}`);
          } else {
            // Unsupported document type (PDF, Word, etc.)
            console.log(`❌ Unsupported document attachment | MIME: ${mimeType} | Filename: ${message.document.filename || 'unknown'}`);
            logger.debug(`📎 Unsupported document attachment | MIME: ${mimeType} | Filename: ${message.document.filename || 'unknown'}`);
            res.status(200).send("EVENT_RECEIVED");
            return;
          }
        } else {
          // Ignore other message types (locations, stickers, etc.)
          const messageKeys = Object.keys(message).filter(key => key !== 'from' && key !== 'id' && key !== 'timestamp');
          logger.debug(`Unsupported message type from ${fromNumber}: ${messageKeys[0]}`);
          res.status(200).send("EVENT_RECEIVED");
          return;
        }

        logger.whatsapp(`Message received | From: ${fromNumber} | Type: ${messageType}`);

        // Debug: Log message content structure for media messages
        if (messageType !== 'text') {
          console.log(`📱 Media message structure:`, {
            type: messageType,
            hasId: !!messageContent.id,
            hasMimeType: !!messageContent.mime_type,
            hasFilename: !!messageContent.filename,
            mimeType: messageContent.mime_type,
            filename: messageContent.filename
          });
        }

        // Find the customer associated with this phone number
        const customer = await findCustomerByPhoneNumber(phoneNumberId);

        if (customer) {
          logger.info(`Processing message | From: ${fromNumber} | Customer: ${customer.auth_id} | Type: ${messageType}`);
          
          // Process the message through our AI system
          await processIncomingMessage(
            customer,
            fromNumber,
            messageContent,
            phoneNumberId,
            messageType,
          );
        }
      }

      res.status(200).send("EVENT_RECEIVED");
    } else {
      res.sendStatus(404);
    }
  } catch (error) {
    console.error("Webhook error:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Register customer with WhatsApp Business number
router.post("/register-customer", async (req, res) => {
  try {
    const {
      authId,
      whatsappBusinessAccountId,
      whatsappPhoneNumberId,
      systemAccessToken,
    } = req.body;

    if (!authId || !whatsappBusinessAccountId || !whatsappPhoneNumberId || !systemAccessToken) {
      return res.status(400).json({
        error:
          "Missing required fields: authId, whatsappBusinessAccountId, whatsappPhoneNumberId, systemAccessToken",
      });
    }

    // Verify the WhatsApp credentials using provided system access token
    const isValid = await verifyWhatsAppCredentials(
      whatsappPhoneNumberId,
      systemAccessToken
    );
    if (!isValid) {
      return res.status(400).json({ error: "Invalid WhatsApp credentials or system access token" });
    }

    // Store customer configuration
    const { data, error } = await supabase
      .from("whatsapp_customers")
      .upsert({
        auth_id: authId,
        whatsapp_business_account_id: whatsappBusinessAccountId,
        whatsapp_phone_number_id: whatsappPhoneNumberId,
        system_access_token: systemAccessToken,
        is_active: true,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;

    // Create default follow-up rules for new customer
    try {
      await supabase.rpc("create_default_follow_up_rules", {
        p_auth_id: authId
      });
    } catch (rulesError) {
      console.error("Error creating default follow-up rules:", rulesError);
      // Don't fail the registration if follow-up rules creation fails
    }

    // Remove sensitive data from response
    const safeData = { ...data };
    delete safeData.system_access_token;

    res.json({
      success: true,
      message: "Customer registered successfully",
      data: safeData,
    });
  } catch (error) {
    console.error("Error registering customer:", error);
    res.status(500).json({ error: "Failed to register customer" });
  }
});

// Get customer configuration
router.get("/customer-config", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({ error: "Missing authId parameter" });
    }

    const { data, error } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        return res
          .status(404)
          .json({ error: "Customer configuration not found" });
      }
      throw error;
    }

    res.json({
      success: true,
      data: data,
    });
  } catch (error) {
    console.error("Error getting customer config:", error);
    res.status(500).json({ error: "Failed to get customer configuration" });
  }
});

// Update customer configuration
router.put("/customer-config", async (req, res) => {
  try {
    const {
      authId,
      whatsappBusinessAccountId,
      whatsappPhoneNumberId,
      systemAccessToken,
    } = req.body;

    if (!authId) {
      return res.status(400).json({ error: "Missing authId" });
    }

    const updates = {};

    // If WhatsApp credentials are being updated, verify them first
    if ((whatsappBusinessAccountId && whatsappPhoneNumberId) || systemAccessToken) {
      // Get current config if only partial update
      let currentConfig = null;
      if (!whatsappBusinessAccountId || !whatsappPhoneNumberId || !systemAccessToken) {
        const { data: current } = await supabase
          .from("whatsapp_customers")
          .select("*")
          .eq("auth_id", authId)
          .single();
        currentConfig = current;
      }

      const isValid = await verifyWhatsAppCredentials(
        whatsappPhoneNumberId || currentConfig?.whatsapp_phone_number_id,
        systemAccessToken || currentConfig?.system_access_token
      );
      
      if (!isValid) {
        return res.status(400).json({ error: "Invalid WhatsApp credentials or system access token" });
      }
    }

    // Update fields if provided
    if (whatsappBusinessAccountId) updates.whatsapp_business_account_id = whatsappBusinessAccountId;
    if (whatsappPhoneNumberId) updates.whatsapp_phone_number_id = whatsappPhoneNumberId;
    if (systemAccessToken) updates.system_access_token = systemAccessToken;

    if (Object.keys(updates).length === 0) {
      return res.status(400).json({ error: "No fields to update" });
    }

    updates.updated_at = new Date().toISOString();

    const { data, error } = await supabase
      .from("whatsapp_customers")
      .update(updates)
      .eq("auth_id", authId)
      .eq("is_active", true)
      .select()
      .single();

    if (error) throw error;

    if (!data) {
      return res
        .status(404)
        .json({ error: "Customer configuration not found" });
    }

    // Remove sensitive data from response
    const safeData = { ...data };
    delete safeData.system_access_token;

    res.json({
      success: true,
      message: "Customer configuration updated successfully",
      data: safeData,
    });
  } catch (error) {
    console.error("Error updating customer config:", error);
    res.status(500).json({ error: "Failed to update customer configuration" });
  }
});

// Test WhatsApp connection
router.post("/test-whatsapp", async (req, res) => {
  try {
    const { authId, testPhoneNumber } = req.body;

    if (!authId) {
      return res.status(400).json({ error: "Missing authId" });
    }

    // Get customer configuration
    const { data: customer, error } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        return res
          .status(404)
          .json({ error: "Customer configuration not found" });
      }
      throw error;
    }

    // If test phone number is provided, send a test message
    if (testPhoneNumber) {
      const testMessage =
        "🤖 This is a test message from your WhatsApp chatbot! Your integration is working correctly.";
      await sendWhatsAppMessage(
        testPhoneNumber,
        testMessage,
        customer.whatsapp_phone_number_id,
      );

      return res.json({
        success: true,
        message: "Test message sent successfully",
        sentTo: testPhoneNumber,
      });
    }

    // Otherwise, just verify credentials
    const isValid = await verifyWhatsAppCredentials(
      customer.whatsapp_phone_number_id,
      customer.system_access_token,
    );

    if (isValid) {
      res.json({
        success: true,
        message: "WhatsApp connection is working correctly",
        phoneNumberId: customer.whatsapp_phone_number_id,
      });
    } else {
      res.status(400).json({
        error: "WhatsApp connection failed. Please check your configuration.",
      });
    }
  } catch (error) {
    console.error("Error testing WhatsApp:", error);
    res.status(500).json({ error: "Failed to test WhatsApp connection" });
  }
});

// Get WhatsApp webhook info
router.get("/webhook-info", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({ error: "Missing authId parameter" });
    }

    // Get customer configuration
    const { data: customer, error } = await supabase
      .from("whatsapp_customers")
      .select("whatsapp_phone_number_id, whatsapp_business_account_id")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        return res
          .status(404)
          .json({ error: "Customer configuration not found" });
      }
      throw error;
    }

    // Provide webhook setup information
    const webhookUrl = `${req.protocol}://${req.get("host")}/api/ai/webhook`;
    const verifyToken = process.env.VERIFY_TOKEN;

    res.json({
      success: true,
      data: {
        webhookUrl,
        verifyToken,
        phoneNumberId: customer.whatsapp_phone_number_id,
        businessAccountId: customer.whatsapp_business_account_id,
        instructions: {
          step1: "Go to your Meta for Developers dashboard",
          step2: "Select your WhatsApp Business App",
          step3: "Go to WhatsApp > Configuration",
          step4: `Set Webhook URL to: ${webhookUrl}`,
          step5: `Set Verify Token to: ${verifyToken}`,
          step6: "Subscribe to 'messages' webhook field",
          step7: "Save your configuration",
        },
      },
    });
  } catch (error) {
    console.error("Error getting webhook info:", error);
    res.status(500).json({ error: "Failed to get webhook information" });
  }
});

// Delete customer WhatsApp integration
router.delete("/customer-config", async (req, res) => {
  try {
    const { authId } = req.body;

    if (!authId) {
      return res.status(400).json({ error: "Missing authId" });
    }

    // First get the customer data before deletion for response
    const { data: customerData, error: selectError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .single();

    if (selectError) {
      if (selectError.code === "PGRST116") {
        return res
          .status(404)
          .json({ error: "Customer configuration not found" });
      }
      throw selectError;
    }

    // Delete the customer configuration from database
    const { error: deleteError } = await supabase
      .from("whatsapp_customers")
      .delete()
      .eq("auth_id", authId);

    if (deleteError) throw deleteError;

    res.json({
      success: true,
      message: "WhatsApp integration deleted successfully",
      deletedData: {
        authId: customerData.auth_id,
        whatsappBusinessAccountId: customerData.whatsapp_business_account_id,
        whatsappPhoneNumberId: customerData.whatsapp_phone_number_id,
        deletedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Error deleting customer config:", error);
    res.status(500).json({ error: "Failed to delete WhatsApp integration" });
  }
});

// ===== ORDER PROCESSING HELPER FUNCTIONS =====

// Helper function to format products for AI consumption
function formatProductsForAI(products) {
  if (!products || products.length === 0) {
    return "No products available.";
  }

  let formattedProducts = "";
  let currentCategory = "";

  products.forEach((product, index) => {
    // Group by category
    if (product.category && product.category !== currentCategory) {
      currentCategory = product.category;
      formattedProducts += `\n--- ${currentCategory.toUpperCase()} ---\n`;
    }

    // Format product info
    formattedProducts += `${index + 1}. ${product.name}`;

    if (product.price) {
      formattedProducts += ` - RM${product.price}`;
    }

    if (product.description) {
      formattedProducts += `\n   Description: ${product.description}`;
    }

    if (product.variations && Array.isArray(product.variations) && product.variations.length > 0) {
      formattedProducts += `\n   Variations: ${product.variations.join(", ")}`;
    }

    // Handle stock for products vs services
    if (product.is_service) {
      formattedProducts += `\n   Type: Service (always available)`;
    } else if (product.stock_quantity !== undefined) {
      if (product.stock_quantity > 0) {
        formattedProducts += `\n   Stock: ${product.stock_quantity} available`;
      } else {
        formattedProducts += `\n   Stock: Out of stock`;
      }
    }

    // Add image information (without URL to prevent AI from including it in response)
    if (product.image_url) {
      formattedProducts += `\n   Image: Available`;
    } else {
      formattedProducts += `\n   Image: Not available`;
    }

    formattedProducts += "\n\n";
  });

  return formattedProducts.trim();
}

// Helper function to send product images via WhatsApp
async function sendProductImages(phoneNumber, phoneNumberId, products, accessToken, maxImages = 3) {
  try {
    const { sendWhatsAppImageMessage } = await import('../utils/whatsapp.js');

    // Import OpenAI for dynamic caption generation
    const { OpenAI } = await import('openai');
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // Filter products that have images and limit to maxImages
    const productsWithImages = products
      .filter(product => product.image_url)
      .slice(0, maxImages);

    if (productsWithImages.length === 0) {
      return false; // No images to send
    }

    // Send each product image with caption
    for (const product of productsWithImages) {
      try {
        // Generate AI-powered dynamic caption (can be made configurable later)
        let useAICaptions = true; // TODO: Make this a user setting
        let caption;

        if (useAICaptions) {
          try {
          const captionResponse = await openai.chat.completions.create({
            model: "gpt-4o-mini",
            messages: [
              {
                role: "system",
                content: `Create an engaging, natural caption for a product image. Be conversational and Malaysian in style. Include:
- Product name and price
- Brief appealing description
- Stock/availability info
- Use emojis naturally (not just at the start of lines)
- Keep it concise but enticing
- Use Malaysian expressions like "ya", "lah" when appropriate
- Make it sound like a friendly recommendation`
              },
              {
                role: "user",
                content: `Product: ${product.name}
Price: RM${parseFloat(product.price).toFixed(2)}
Description: ${product.description || 'No description available'}
Stock: ${product.is_service ? 'Service (always available)' : product.stock_quantity > 0 ? `${product.stock_quantity} in stock` : 'Out of stock'}
Category: ${product.category || 'General'}`
              }
            ],
            max_tokens: 150,
            temperature: 0.7,
          });

          caption = captionResponse.choices[0].message.content.trim();
          console.log(`🤖 Generated AI caption for ${product.name}: "${caption}"`);
          } catch (aiError) {
            console.error(`Error generating AI caption for ${product.name}:`, aiError);
            // Fallback to original format if AI fails
            useAICaptions = false;
          }
        }

        if (!useAICaptions) {
          // Use traditional structured format
          caption = `📸 *${product.name}*`;
          if (product.price) {
            caption += `\n💰 RM${parseFloat(product.price).toFixed(2)}`;
          }
          if (product.description) {
            caption += `\n📝 ${product.description}`;
          }
          if (product.is_service) {
            caption += `\n⚡ Available`;
          } else if (product.stock_quantity > 0) {
            caption += `\n📦 ${product.stock_quantity} in stock`;
          } else {
            caption += `\n❌ Out of stock`;
          }
        }

        // Send image directly using URL (same approach as knowledge base images)
        await sendWhatsAppImageMessage(
          phoneNumber,
          product.image_url, // Use image URL directly
          phoneNumberId,
          accessToken,
          caption
        );

        // Small delay between images to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (imageError) {
        console.error(`Error sending image for product ${product.name}:`, imageError);
        // Continue with next product
      }
    }

    return true; // Successfully sent at least one image
  } catch (error) {
    console.error("Error sending product images:", error);
    return false;
  }
}

// Helper function to get products with images for AI recommendations
async function getProductsWithImages(authId, includeImages = false) {
  try {
    const { data: products, error } = await supabase
      .from("product_catalog")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .order("category", { ascending: true })
      .order("name", { ascending: true });

    if (error) throw error;

    if (includeImages) {
      // Filter products that have images for visual recommendations
      return products.filter(product => product.image_url);
    }

    return products || [];
  } catch (error) {
    console.error("Error fetching products:", error);
    return [];
  }
}

// Generate response for order processing using OpenAI
async function generateOrderResponse(prompt, customer, phoneNumber, model = "gpt-4o-mini", systemPrompt, existingOrder, whatsappConfig, rawTranscription = null) {
  const startTime = Date.now();

  try {
    // Update/create contact information
    await supabase.rpc("upsert_contact", {
      p_auth_id: customer.auth_id,
      p_phone_number: phoneNumber,
    });

    // Track incoming message statistics (will be updated with actual values later)
    await Promise.all([
      supabase.from("message_statistics").insert([
        {
          auth_id: customer.auth_id,
          phone_number: phoneNumber,
          message_type: "incoming",
          message_length: prompt.length,
          knowledge_sections_found: 0, // Will be updated with actual values later
          similarity_score: 0, // Will be updated with actual values later
        },
      ]),
      // Increment message usage count
      supabase.rpc("increment_message_usage", {
        p_auth_id: customer.auth_id,
        p_message_type: "incoming",
      }),
      // Track incoming message analytics
      updateAnalytics(customer.auth_id, phoneNumber, {
        messageType: 'incoming',
        knowledgeQuery: false,
        knowledgeHit: false,
        knowledgeSectionsFound: 0,
        similarityScore: 0,
        responseTime: 0,
        totalTokens: 0,
        promptTokens: 0,
        completionTokens: 0
      })
    ]);

    // Get chatbot settings for knowledge base configuration
    const { data: settings, error: settingsError } = await supabase
      .from("chatbot_settings")
      .select("similarity_threshold, match_count")
      .eq("auth_id", customer.auth_id)
      .single();

    if (settingsError) {
      console.error("Error getting settings for knowledge base:", settingsError);
    }

    const similarityThreshold = settings?.similarity_threshold || 0.40;
    const matchCount = settings?.match_count || 5;

    // Convert the question into an embedding for knowledge base search
    // Use raw transcription for knowledge base search if available (for voice messages)
    const searchText = rawTranscription || prompt;
    const embeddingResponse = await openai.embeddings.create({
      model: "text-embedding-3-small",
      input: searchText,
    });

    const questionEmbedding = embeddingResponse.data[0].embedding;

    // Search the knowledge base for relevant information
    const { data: matchingSections, error: kbError } = await supabase.rpc(
      "match_knowledge_sections",
      {
        query_embedding: questionEmbedding,
        match_threshold: similarityThreshold,
        match_count: matchCount,
        p_auth_id: customer.auth_id,
      },
    );

    if (kbError) {
      console.error("Error searching knowledge base:", kbError);
    }

    // Combine relevant knowledge sections
    const knowledgeContext = matchingSections && matchingSections.length > 0
      ? `\n\nRELEVANT KNOWLEDGE:\n${matchingSections.map((section) => section.content).join("\n\n")}`
      : '';

    // Get best similarity score for statistics
    const bestSimilarityScore = matchingSections && matchingSections.length > 0
      ? Math.max(...matchingSections.map((section) => section.similarity))
      : 0;

    // Get recent chat history for context (use same limit as regular chat)
    // Get user settings for chat history limit
    const { data: chatSettings, error: chatSettingsError } = await supabase
      .from("chatbot_settings")
      .select("chat_history_limit")
      .eq("auth_id", customer.auth_id)
      .single();

    const chatHistoryLimit = chatSettings?.chat_history_limit || 10; // Default to 10 if not set

    const { data: chatHistory, error: historyError } = await supabase
      .from("chat_history")
      .select("*")
      .eq("auth_id", customer.auth_id)
      .eq("phone_number", phoneNumber)
      .order("created_at", { ascending: false })
      .limit(chatHistoryLimit); // Use user's chat history limit setting

    if (historyError) throw new Error(historyError.message);

    // Prepare conversation history
    const conversationHistory = chatHistory
      ? chatHistory.reverse().map((msg) => ({
          role: msg.role,
          content: msg.content,
        }))
      : [];

    // Add existing order context if available
    // Determine required fields from Google Sheets configuration
    const requiredFields = await getRequiredFields(customer, whatsappConfig);

    console.log(`📋 Required fields for order: ${requiredFields.join(', ')}`);

    // Create progressive form display
    const createProgressiveForm = (customerInfo, requiredFields) => {
      const form = {};
      requiredFields.forEach(field => {
        let value = '';
        switch(field) {
          case 'name':
            value = customerInfo.name || customerInfo.customer_name || customerInfo['Customer Name'] || '';
            break;
          case 'phone':
            value = customerInfo.phone || customerInfo.customer_phone || customerInfo['Customer Phone'] || phoneNumber || '';
            break;
          case 'address':
            value = customerInfo.address || customerInfo.customer_address || customerInfo['Customer Address'] || customerInfo.location || '';
            break;
          case 'email':
            value = customerInfo.email || customerInfo.customer_email || customerInfo['Customer Email'] || '';
            break;
          case 'datetime':
            value = customerInfo.datetime || customerInfo.appointment_time || customerInfo['Date & Time'] || customerInfo.date || customerInfo.delivery_date || '';
            break;
        }
        form[field] = value;
      });
      return form;
    };

    const customerInfo = existingOrder?.customer_info || {};
    const progressiveForm = createProgressiveForm(customerInfo, requiredFields);

    // Create form display
    const formDisplay = requiredFields.map(field => {
      const value = progressiveForm[field];
      const displayName = field.charAt(0).toUpperCase() + field.slice(1);
      return `${displayName}: ${value || ''}`;
    }).join('\n');

    const orderContext = existingOrder
      ? `\n\nEXISTING ORDER CONTEXT (USE THIS INFORMATION - DON'T ASK AGAIN):
- Order ID: ${existingOrder.id}
- Status: ${existingOrder.order_status}
- Order Summary: ${existingOrder.order_summary || 'No summary yet'}

CURRENT ORDER FORM:
${formDisplay}

REQUIRED FIELDS TO COLLECT: ${requiredFields.join(', ')}
IMPORTANT: Show the updated form after each field is collected. If customer information is already available above, DO NOT ask for it again. Use the existing information and continue with missing details only.`
      : `\n\nNEW ORDER: Starting fresh order collection process.

CURRENT ORDER FORM:
${formDisplay}

REQUIRED FIELDS TO COLLECT: ${requiredFields.join(', ')}
IMPORTANT: Show the progressive form after each field is collected.`;

    // Debug: Log order context when customer mentions order-related keywords
    if (/order|place|tomorrow|today|name|phone|address/i.test(prompt)) {
      console.log(`📋 Debug - Order context for message "${prompt}":`);
      console.log(`🔄 Existing order: ${existingOrder ? 'YES' : 'NO'}`);
      if (existingOrder) {
        console.log(`📝 Customer info already collected:`, existingOrder.customer_info);
      }
      console.log(`📞 Phone number from WhatsApp: ${phoneNumber}`);
    }

    // Detect current message language
    const detectLanguage = (text) => {
      // Simple language detection based on character patterns
      const chinesePattern = /[\u4e00-\u9fff]/;
      const malayPattern = /\b(ada|apa|boleh|tak|tidak|saya|kami|dengan|untuk|dari|ini|itu|yang|dan|atau|juga|sudah|belum|akan|bisa|mau|nak|lah|kah|pun|je|la|ke|di|pada|dalam|atas|bawah|depan|belakang|kiri|kanan)\b/i;

      if (chinesePattern.test(text)) {
        return 'chinese';
      } else if (malayPattern.test(text)) {
        return 'malay';
      } else {
        return 'english';
      }
    };

    const currentLanguage = detectLanguage(prompt);
    console.log(`🌍 Order processing - Detected language for "${prompt}": ${currentLanguage}`);

    // Add language instruction to system prompt
    const languageInstruction = {
      'english': 'IMPORTANT: The customer just wrote in ENGLISH. You MUST respond in ENGLISH, regardless of previous conversation language.',
      'chinese': 'IMPORTANT: The customer just wrote in CHINESE. You MUST respond in CHINESE, regardless of previous conversation language.',
      'malay': 'IMPORTANT: The customer just wrote in MALAY. You MUST respond in MALAY, regardless of previous conversation language.'
    };

    // Enhance user's simple order prompt with technical implementation details
    const enhancedOrderPrompt = `${systemPrompt}

TECHNICAL IMPLEMENTATION (SYSTEM INSTRUCTIONS):
- LANGUAGE DETECTION: Match customer (English/Malay/Chinese/mix). Default: English. Use "ya", "kan" naturally. Add emojis: 📱 orders, 🎁 promotions, 📍 delivery.
- ORDER & BOOKING PROCESSING: Handle both product orders and service bookings. For products: Show prices, colors, sizes, stock. For services: Show available times, duration, requirements. ALWAYS show the progressive form after each field is collected. Format: "Name: [value]\nPhone: [value]\nAddress/Location: [value]\nDate/Time: [value]" then ask for the next missing field. ALWAYS calculate and show total payment when asking for confirmation. Collect details one by one: "What's your full name ya?" / "What's your phone number?" / "Which area you staying? Need your full address 📍" / "When would you like this scheduled?". Don't ask for the same information twice - if customer already provided date/time information, use it for both date and datetime fields.
- COMPLETION: NEVER say "ORDER_COMPLETE:" unless ALL required fields are collected AND customer explicitly confirms with "yes", "confirm", "ok" etc. Always ask "Would you like to confirm this order/booking?" and wait for confirmation. Only after confirmation, show structured summary:

FOR ORDERS: "Awesome! Here's the summary of your order:\n\n- *Customer Name:* [name]\n- *Customer Phone:* [phone]\n- *Date:* [date/delivery info]\n- *Product:* [product details]\n- *Variation:* [if any]\n- *Quantity:* [quantity]\n- *Total Amount:* [ALWAYS calculate: quantity × unit price = total]\n- *Status:* Pending\n\nYour order has been successfully placed! Thank you for choosing us! 🎉"

FOR BOOKINGS: "Awesome! Here's the summary of your booking:\n\n- *Customer Name:* [name]\n- *Customer Phone:* [phone]\n- *Date & Time:* [scheduled date and time]\n- *Service:* [service details]\n- *Duration:* [if applicable]\n- *Location:* [address or venue]\n- *Total Amount:* [total cost]\n- *Status:* Confirmed\n\nYour booking has been successfully confirmed! We look forward to seeing you! 🎉"

Then say "ORDER_COMPLETE:" + basic summary for system processing.
- RESPONSE STYLE: Give COMPLETE info directly. DON'T ask follow-ups or offer more details. If unsure: "Eh sorry, not sure about that".
- EXAMPLES: "Hi" → "Hi there! What would you like to order today? 😊" | "你好" → "你好！今天想要order什么呢？😊" | "Where deliver?" → "Where should we deliver ya? Need your full address 📍"
- MULTIPLE MESSAGES: Use ||| ONLY for lists/categories/promotions: "We got these categories! 😊|||📱 Electronics|||👕 Fashion|||🍔 Food|||Which category you interested in?" For regular responses, keep as single message - AI will auto-split if needed.

CRITICAL ORDER MEMORY RULE:
- ALWAYS check the "EXISTING ORDER CONTEXT" section below FIRST before asking for any customer information
- If customer information is already listed in the existing order context, DO NOT ask for it again
- Phone number is ALWAYS available from WhatsApp - never ask for it
- If customer says "tomorrow", "today", "next week" - treat as delivery date, don't ask for clarification
- Check the "REQUIRED FIELDS TO COLLECT" list and ensure ALL fields are collected before completing order
- NEVER EVER say "ORDER_COMPLETE:" unless ALL required fields from the list have been collected
- If ANY required field is missing, ask for it instead of completing the order
- Only ask for information that is missing or incomplete
- When customer asks about order status, refer to the existing order context to provide current status

VALIDATION BEFORE COMPLETION:
Before saying "ORDER_COMPLETE:", verify you have collected ALL of these required fields: ${requiredFields.join(', ')}
If any field is missing, show the current form and ask for the missing field.
IMPORTANT: Don't ask for the same information twice. If customer provided "tomorrow" for date, that satisfies both date and datetime requirements.
After all fields are collected, show the complete form with TOTAL PAYMENT and ask "Would you like to confirm this order for [product] at [total amount]?" and wait for explicit confirmation.
When customer confirms, show the structured order summary first, then say "ORDER_COMPLETE:" for system processing.

FORM DISPLAY FORMAT:
Always show the order form in this format:
Name: [customer name or empty]
Phone: [phone number or empty]
Address: [address or empty]
[other required fields...]

ORDER COMPLETION FORMAT:
When order is confirmed, show this exact format:
"Awesome! Here's the summary of your order:

- *Customer Name:* [name]
- *Customer Phone:* [phone]
- *Date:* [delivery date/time]
- *Product:* [product details]
- *Variation:* [variation if any]
- *Quantity:* [quantity]
- *Total Amount:* [total price]
- *Status:* Pending

Your order has been successfully placed! Thank you for choosing us! 🎉"

BOOKING COMPLETION FORMAT:
When booking is confirmed, show this exact format:
"Awesome! Here's the summary of your booking:

- *Customer Name:* [name]
- *Customer Phone:* [phone]
- *Date & Time:* [scheduled date and time]
- *Service:* [service details]
- *Duration:* [if applicable]
- *Location:* [address or venue]
- *Total Amount:* [total cost]
- *Status:* Confirmed

Your booking has been successfully confirmed! We look forward to seeing you! 🎉"

CURRENT CONTEXT (PRIORITY - USE THIS FIRST):
${knowledgeContext}
${orderContext}

${languageInstruction[currentLanguage]}

MEMORY USAGE: If conversation history contains relevant information, use it only if the current context above doesn't already provide the answer. Always prioritize current context over memory.

IMAGES: Never include URLs/links. When customers ask to see products/photos:
- IMPORTANT: Look for "Image: Available" in the product information
- If ANY product shows "Image: Available", respond positively (e.g., "Let me show you!" or "Here's how it looks!") - the system will automatically send the images
- ONLY say "Sorry, no photos available 😅" if ALL products show "Image: Not available"
- Always check the AVAILABLE PRODUCTS section for image status before responding`;

    // Use OpenAI to generate a response
    const aiResponse = await openai.chat.completions.create({
      model: model,
      messages: [
        {
          role: "system",
          content: enhancedOrderPrompt,
        },
        ...conversationHistory,
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.3, // Lower temperature for more consistent responses
      max_tokens: 800, // Increased to accommodate product suggestions and knowledge
    });

    const answer = aiResponse.choices[0].message.content;
    const responseTime = Date.now() - startTime;

    // Extract token usage from OpenAI response
    const usage = aiResponse.usage || {};
    const promptTokens = usage.prompt_tokens || 0;
    const completionTokens = usage.completion_tokens || 0;
    const totalTokens = usage.total_tokens || 0;

    // Store the conversation in chat history with proper timing
    const userTimestamp = new Date().toISOString();

    // Store user message first
    await supabase.from("chat_history").insert({
      auth_id: customer.auth_id,
      phone_number: phoneNumber,
      role: "user",
      content: prompt,
      created_at: userTimestamp,
    });

    // Store assistant message with slightly later timestamp
    const assistantTimestamp = new Date(Date.now() + 1000).toISOString(); // 1 second later
    await supabase.from("chat_history").insert({
      auth_id: customer.auth_id,
      phone_number: phoneNumber,
      role: "assistant",
      content: answer,
      created_at: assistantTimestamp,
    });

    // Track outgoing message statistics
    await Promise.all([
      supabase.from("message_statistics").insert([
        {
          auth_id: customer.auth_id,
          phone_number: phoneNumber,
          message_type: "outgoing",
          model_used: model,
          prompt_tokens: promptTokens,
          completion_tokens: completionTokens,
          total_tokens: totalTokens,
          response_time_ms: responseTime,
          knowledge_sections_found: matchingSections ? matchingSections.length : 0,
          similarity_score: bestSimilarityScore,
          message_length: answer.length,
        },
      ]),
      // Increment outgoing message usage count
      supabase.rpc("increment_message_usage", {
        p_auth_id: customer.auth_id,
        p_message_type: "outgoing",
      }),
    ]);

    // Update analytics (now includes knowledge base usage)
    const knowledgeQuery = true; // Order processing now uses knowledge base
    const knowledgeHit = matchingSections && matchingSections.length > 0;

    await updateAnalytics(customer.auth_id, phoneNumber, {
      messageType: 'outgoing',
      knowledgeQuery,
      knowledgeHit,
      knowledgeSectionsFound: matchingSections ? matchingSections.length : 0,
      similarityScore: bestSimilarityScore,
      responseTime,
      totalTokens,
      promptTokens,
      completionTokens
    });

    // Automatically send images from matched knowledge base sections (with AI decision)
    await sendKnowledgeBaseImages(customer.auth_id, phoneNumber, matchingSections, prompt);

    // Check if the user is asking for product recommendations in order context
    const isProductInquiry = /\b(show|see|picture|image|photo|recommend|suggest|what.*have|available.*product|catalog|menu|options|look.*like|how.*look|appearance|visual|pic|img|pix|pics|images|photos|can.*see|how.*ur.*look|how.*your.*look)\b/i.test(prompt.toLowerCase()) ||
                            /(照片|图片|图像|相片|看看|显示|展示|推荐|建议|有什么|产品|菜单|样子|长什么样|外观|视觉|相|图|照|样)/i.test(prompt) ||
                            /\b(gambar|foto|tunjuk|tunjukkan|tengok|lihat|papar|cadang|cadangan|apa.*ada|produk|menu|rupa|macam.*mana|penampilan|visual|gbr|pic)\b/i.test(prompt.toLowerCase()) ||
                            /(有pic|有图|有照|看pic|看图|看照|show pic|show图|要pic|要图|要照|send pic|send图|send照|看一下|看下|给我看|可以.*看)/i.test(prompt.toLowerCase());

    if (isProductInquiry) {
      console.log(`🔍 Product inquiry detected in order mode: "${prompt}"`);
      try {
        // Get all products with images
        const allProductsWithImages = await getProductsWithImages(customer.auth_id, true);
        console.log(`📦 Found ${allProductsWithImages.length} products with images for authId: ${customer.auth_id}`);
        if (allProductsWithImages.length > 0) {
          // Use AI to determine if we should send product images
          const imageDecisionResponse = await openai.chat.completions.create({
            model: "gpt-4o-mini",
            messages: [
              {
                role: "system",
                content: `Determine if customer wants to see product images. Reply 'YES' if customer is:
- Asking to see/show/look at products ("can i see", "show me", "how does it look", "what does it look like")
- Asking for pictures/photos/images ("any pics", "got photo", "can see picture")
- Asking about appearance/visual ("how it looks", "what it looks like")
- Browsing products ("what do you have", "show me options")
Reply 'NO' for general questions, pricing only, or when not asking for visual content.`
              },
              {
                role: "user",
                content: `Customer message: "${prompt}"\nSend product images?`
              }
            ],
            max_tokens: 10,
            temperature: 0.1,
          });

          const shouldSendImages = imageDecisionResponse.choices[0].message.content.trim().toUpperCase() === 'YES';
          console.log(`🤖 AI decision for sending images: ${shouldSendImages ? 'YES' : 'NO'} (response: "${imageDecisionResponse.choices[0].message.content.trim()}")`);

          if (shouldSendImages) {
            // Use AI to determine which specific products are relevant to the customer's query
            const relevantProductsResponse = await openai.chat.completions.create({
              model: "gpt-4o-mini",
              messages: [
                {
                  role: "system",
                  content: `Return relevant product names as JSON array only. No extra text.
Examples:
"blueberry scone" → ["Blueberry Scone"]
"avocado toast" → ["Avocado Toast"]
"how ur avocado toast look" → ["Avocado Toast"]
"what desserts" → ["Blueberry Scone", "Chocolate Cake"]
"show me food" → ["Avocado Toast", "Blueberry Scone"]
Match products by name, partial name, or category. Be flexible with spelling and casual language.`
                },
                {
                  role: "user",
                  content: `Message: "${prompt}"\nProducts: ${JSON.stringify(allProductsWithImages.map(p => p.name))}`
                }
              ],
              max_tokens: 200,
              temperature: 0.1,
            });

            let relevantProductNames = [];
            try {
              // Clean the response by removing markdown code blocks and extra formatting
              let responseContent = relevantProductsResponse.choices[0].message.content.trim();

              // Remove markdown code blocks (```json ... ``` or ``` ... ```)
              responseContent = responseContent.replace(/```(?:json)?\s*([\s\S]*?)\s*```/g, '$1');

              // Remove any leading/trailing whitespace again
              responseContent = responseContent.trim();

              // Try to parse as JSON
              relevantProductNames = JSON.parse(responseContent);

              // Ensure it's an array
              if (!Array.isArray(relevantProductNames)) {
                console.error("Relevant products response is not an array:", relevantProductNames);
                relevantProductNames = [];
              }
            } catch (parseError) {
              console.error("Error parsing relevant products response:", parseError);
              console.error("Raw response:", relevantProductsResponse.choices[0].message.content);
              // Fallback: if parsing fails, don't send any images to avoid sending irrelevant ones
              relevantProductNames = [];
            }

            // Filter products to only include relevant ones
            const relevantProducts = allProductsWithImages.filter(product =>
              relevantProductNames.includes(product.name)
            );
            console.log(`🎯 Relevant products found: ${relevantProducts.map(p => p.name).join(', ')} (from AI response: ${relevantProductNames.join(', ')})`);

            if (relevantProducts.length > 0) {
              return {
                text: answer,
                sendProductImages: true,
                products: relevantProducts.slice(0, 3), // Limit to 3 images
                phoneNumber: phoneNumber
              };
            } else if (relevantProductNames.length > 0) {
              // Customer asked for specific products but none have images
              // Check if the mentioned products exist (even without images)
              const allProducts = await getProductsWithImages(authId, false); // Get all products
              const mentionedProductsExist = allProducts.filter(product =>
                relevantProductNames.includes(product.name)
              );

              if (mentionedProductsExist.length > 0) {
                // Products exist but don't have images
                const productNames = mentionedProductsExist.map(p => p.name).join(", ");
                const apologyMessage = mentionedProductsExist.length === 1
                  ? `Sorry, I don't have a photo of ${productNames} available right now 😅 But I can tell you more about it if you'd like!`
                  : `Sorry, I don't have photos of ${productNames} available right now 😅 But I can tell you more about them if you'd like!`;

                return {
                  text: `${answer}\n\n${apologyMessage}`,
                  sendProductImages: false,
                  products: [],
                  phoneNumber: phoneNumber
                };
              }
            }
          }
        }
      } catch (imageError) {
        console.error("Error preparing product images for order:", imageError);
        // Continue with text-only response
      }
    }

    return answer;
  } catch (error) {
    console.error("Error in generateOrderResponse:", error);

    // Track failed response
    const responseTime = Date.now() - startTime;
    await Promise.all([
      supabase.from("message_statistics").insert([
        {
          auth_id: authId,
          phone_number: phoneNumber,
          message_type: "outgoing",
          model_used: model,
          response_time_ms: responseTime,
          knowledge_sections_found: 0,
          similarity_score: 0,
          message_length: 0,
        },
      ]),
      updateAnalytics(authId, phoneNumber, {
        messageType: 'outgoing',
        knowledgeQuery: true,
        knowledgeHit: false,
        knowledgeSectionsFound: 0,
        similarityScore: 0,
        responseTime,
        totalTokens: 0,
        promptTokens: 0,
        completionTokens: 0
      }),
    ]).catch((err) => console.error("Error tracking failed response:", err));

    throw error;
  }
}



// Helper function to handle order processing
async function handleOrderProcessing(customer, fromNumber, message, phoneNumberId, existingOrder, rawTranscription = null) {
  try {
    // Get customer's WhatsApp configuration for spreadsheet columns
    const { data: whatsappConfig, error: configError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", customer.auth_id)
      .single();

    if (configError) {
      console.error("Error getting WhatsApp config:", configError);
      await sendWhatsAppMessage(
        fromNumber,
        "Sorry, there was an error processing your order. Please try again later.",
        phoneNumberId,
      );
      return;
    }

    // Get chatbot settings for order system prompt
    const { data: settings, error: settingsError } = await supabase
      .from("chatbot_settings")
      .select("*")
      .eq("auth_id", customer.auth_id)
      .single();

    if (settingsError) {
      console.error("Error getting chatbot settings:", settingsError);
      await sendWhatsAppMessage(
        fromNumber,
        "Sorry, there was an error processing your order. Please try again later.",
        phoneNumberId,
      );
      return;
    }

    // Create enhanced order system prompt with spreadsheet columns
    // Get product catalog for this business
    const { data: products, error: productsError } = await supabase
      .from("product_catalog")
      .select("*")
      .eq("auth_id", customer.auth_id)
      .eq("is_active", true)
      .order("category", { ascending: true })
      .order("name", { ascending: true });

    if (productsError) {
      console.error("Error fetching products for order:", productsError);
    }

    // Format product catalog for AI
    const formattedProductsForOrder = formatProductsForAI(products);
    const productCatalog = products && products.length > 0
      ? `\n\nAVAILABLE PRODUCTS:\n${formattedProductsForOrder}`
      : '\n\nNo products available in catalog.';

    // Debug: Log product context when customer asks about specific products in order mode
    if (/avocado.*toast/i.test(message) || /can.*see.*how.*look/i.test(message)) {
      console.log(`🥑 Debug - Order mode product context for visual inquiry: "${message}"`);
      console.log(`📦 Products found: ${products ? products.length : 0}`);
      console.log(`🖼️ Products with images: ${products ? products.filter(p => p.image_url).length : 0}`);
      console.log(`📝 Formatted products for order mode:`);
      console.log(formattedProductsForOrder);
      console.log(`🎯 Product catalog being sent to AI:`);
      console.log(productCatalog);
    }

    // Check if customer is asking about order tracking
    const isOrderTrackingQuery = /\b(track|status|order|booking|appointment|reservation|my.*order|order.*status|where.*order|check.*order|order.*update|delivery|shipped|confirm|confirmed|yesterday|past|previous|old|placed|when|progress|ready)\b/i.test(message.toLowerCase()) ||
                                 /(订单|状态|跟踪|追踪|我的订单|订单状态|预约|预定|确认)/i.test(message) ||
                                 /\b(jejak|status|pesanan|tempahan|temujanji|order|saya.*order|order.*saya|hantar|pos|confirm|mana|bila|sudah|siap)\b/i.test(message.toLowerCase()) ||
                                 /\b(where.*is|how.*is|what.*status|order.*now|my.*order|order.*ready|order.*done)\b/i.test(message.toLowerCase());

    let orderTrackingContext = '';

    if (isOrderTrackingQuery) {
      try {
        console.log(`🔍 Order tracking query detected: "${message}"`);

        // Get order status from both database and Google Sheets
        let orderStatus = { found: false, orders: [], count: 0 };

        // First, check database for recent orders
        try {
          const { data: dbOrders, error: dbError } = await supabase
            .from("orders")
            .select("*")
            .eq("auth_id", customer.auth_id)
            .eq("phone_number", fromNumber)
            .order("created_at", { ascending: false })
            .limit(5);

          if (!dbError && dbOrders && dbOrders.length > 0) {
            console.log(`📋 Found ${dbOrders.length} order(s) in database for ${fromNumber}`);
            console.log(`📋 Database orders:`, dbOrders.map(o => ({
              id: o.id,
              status: o.order_status,
              phone: o.phone_number,
              summary: o.order_summary?.substring(0, 50) + '...'
            })));

            const dbOrdersFormatted = dbOrders.map(order => {
              // Security check: verify phone number matches
              const orderPhone = order.phone_number;
              const cleanOrderPhone = orderPhone ? orderPhone.replace(/\D/g, '') : '';
              const cleanSearchPhone = fromNumber.replace(/\D/g, '');

              if (!cleanOrderPhone.includes(cleanSearchPhone) && !cleanSearchPhone.includes(cleanOrderPhone)) {
                console.warn(`📋 Phone number mismatch for order ${order.id}: ${orderPhone} vs ${fromNumber}`);
                return null; // Don't return orders that don't match phone number
              }

              // Generate order ID: use user_order_id if available, otherwise create from database ID
              const displayOrderId = order.user_order_id || `ORD${order.id}`;

              return {
                rowIndex: `DB-${order.id}`,
                details: {
                  'Order ID': displayOrderId,
                  'Status': order.order_status || 'Pending',
                  'Product': order.order_summary || 'Order details',
                  'Customer Info': JSON.stringify(order.customer_info || {}),
                  'Created': new Date(order.created_at).toLocaleDateString(),
                  'Phone': order.phone_number
                }
              };
            }).filter(Boolean); // Remove null entries from phone number mismatches

            orderStatus = {
              found: true,
              orders: dbOrdersFormatted,
              count: dbOrders.length
            };
          } else {
            console.log(`📋 No orders found in database for ${fromNumber}`);
            if (dbError) {
              console.log(`📋 Database error:`, dbError);
            }
          }
        } catch (dbError) {
          console.error("Error checking database orders:", dbError);
        }

        // If no database orders found, try Google Sheets (only if configured)
        if (!orderStatus.found && (whatsappConfig.spreadsheet_id || whatsappConfig.google_sheets_url)) {
          try {
            orderStatus = await getOrderStatusFromGoogleSheets(whatsappConfig, fromNumber);
            console.log(`📊 Google Sheets order check result: ${orderStatus.found ? 'Found' : 'Not found'}`);
          } catch (sheetsError) {
            console.error("Error checking Google Sheets orders:", sheetsError);
          }
        } else if (!orderStatus.found) {
          console.log(`📊 Google Sheets not configured for customer ${customer.auth_id}, skipping Google Sheets order tracking`);
        }

        if (orderStatus.found && orderStatus.orders && orderStatus.orders.length > 0) {
          orderTrackingContext = `\n\nORDER TRACKING INFORMATION (PRIORITY - USE THIS FIRST):
Found ${orderStatus.count} order(s) for this customer:

${orderStatus.orders.map((order, index) => {
  const details = order.details;
  const orderId = details['Order ID'] || `ROW${order.rowIndex}`;
  const status = details['Status'] || details['status'] || 'Pending';
  const product = details['Product'] || details['product'] || details['Item'] || 'Order details';
  const date = details['Date'] || details['date'] || details['Created'] || 'Not specified';

  const formattedDetails = Object.entries(details)
    .filter(([key, value]) => value && value.toString().trim())
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n');

  return `Order ${index + 1}:
- Order ID: ${orderId}
- Status: ${status}
- Product: ${product}
- Date: ${date}
- Full Details: ${formattedDetails}`;
}).join('\n\n')}

INSTRUCTIONS: Provide this order information to the customer in a friendly, conversational way.
IMPORTANT SECURITY: Only show order information if the phone number matches the order.
IMPORTANT DISPLAY RULES:
1. NEVER show internal row numbers (like DB-123 or ROW5) to customers
2. Use the Order ID field for customer reference (if it starts with ROW, present it as a tracking number)
3. Show order status, product details, and relevant dates in a customer-friendly format
4. If Order ID starts with "ROW", present it as "Tracking Number: ROW123" instead of "Order ID: ROW123"
DO NOT ask for more information - just provide the order details found.`;

          console.log(`📋 Found ${orderStatus.count} order(s) for ${fromNumber}`);
        } else {
          orderTrackingContext = `\n\nORDER TRACKING INFORMATION:
No orders found for this phone number in our records. The customer might be asking about order tracking, but we don't have any order information for them.

INSTRUCTIONS: Politely inform the customer that no orders were found for their phone number and suggest they contact the business directly or check if they used a different phone number.`;

          console.log(`📋 No orders found for ${fromNumber}`);
        }
      } catch (error) {
        console.error("Error checking order tracking in order processing mode:", error);
        orderTrackingContext = `\n\nORDER TRACKING INFORMATION:
Unable to check order status at the moment due to a technical issue.

INSTRUCTIONS: Apologize for the technical issue and suggest the customer try again later or contact support directly.`;
      }
    }

    // Enhanced order system prompt with spreadsheet columns and product catalog
    const columnsText = (whatsappConfig.spreadsheet_columns && Array.isArray(whatsappConfig.spreadsheet_columns))
      ? whatsappConfig.spreadsheet_columns.join(", ")
      : 'OrderID, Date, Time, Customer Phone, Customer Name, Address, Product, Variation, Quantity, Total Amount, Status';
    const enhancedSystemPrompt = `${settings.order_system_prompt}

IMPORTANT COLLECTION FIELDS: You must collect information for these specific fields: ${columnsText}

${productCatalog}

PRODUCT GUIDELINES:
- Always reference the AVAILABLE PRODUCTS list above when customers ask what you have
- Suggest products based on customer preferences or needs
- Mention prices, descriptions, variations, and stock availability
- If customer asks for something not in the catalog, suggest similar alternatives
- Don't take orders for out-of-stock items

${orderTrackingContext}

IMPORTANT: The detailed order context and customer information will be provided in the system prompt. Use that information to avoid asking for details that have already been collected.

For order confirmation keywords to use:
- When all information is collected: "ORDER_COMPLETE: [summary]"
- If customer wants to cancel: "ORDER_CANCELLED"`;

    // Generate response using order-specific prompt
    const response = await generateOrderResponse(
      message,
      customer, // Pass customer object instead of just auth_id
      fromNumber,
      settings.model,
      enhancedSystemPrompt,
      existingOrder,
      whatsappConfig, // Pass whatsapp config for spreadsheet columns
      rawTranscription, // Pass raw transcription for better knowledge base search
    );

    // Handle enhanced response format or regular text response
    let textResponse = response;
    let shouldSendProductImages = false;
    let productsToSend = [];

    if (typeof response === 'object' && response.sendProductImages) {
      textResponse = response.text;
      shouldSendProductImages = true;
      productsToSend = response.products;
    }

    // Check for order completion or cancellation
    if (textResponse.includes("ORDER_COMPLETE")) {
      // Validate that all required fields are collected before completing
      const missingFields = await validateRequiredFields(customer, fromNumber, existingOrder, whatsappConfig);

      if (missingFields.length > 0) {
        console.log(`❌ Order completion blocked - missing fields: ${missingFields.join(', ')}`);

        // Override the response to ask for missing information
        const missingFieldsPrompt = missingFields.map(field => {
          switch(field) {
            case 'address': return "Which area you staying? Need your full address 📍";
            case 'name': return "What's your full name ya?";
            case 'phone': return "What's your phone number?";
            case 'email': return "What's your email address?";
            case 'datetime': return "When would you like to schedule this? Please provide date and time 📅";
            default: return `Please provide your ${field}`;
          }
        }).join(' ');

        textResponse = `I still need some information to complete your order. ${missingFieldsPrompt}`;

        // Update existing order with new information
        await updateOrCreateOrder(customer, fromNumber, message, existingOrder);
      } else {
        console.log(`✅ All required fields collected - completing order`);
        await completeOrder(customer, fromNumber, textResponse, existingOrder, whatsappConfig);
      }
    } else if (textResponse.includes("ORDER_CANCELLED")) {
      await cancelOrder(customer, fromNumber, existingOrder);
    } else {
      // Update existing order with new information or create new order
      await updateOrCreateOrder(customer, fromNumber, message, existingOrder);
    }

    // Send text response via WhatsApp (supports multiple messages)
    const cleanResponse = textResponse.replace(/ORDER_(COMPLETE|CANCELLED):?\s*/g, "");
    await sendMultipleWhatsAppMessages(fromNumber, cleanResponse, phoneNumberId);

    // Send product images if needed
    if (shouldSendProductImages && productsToSend.length > 0) {
      try {
        const imagesSent = await sendProductImages(
          fromNumber,
          phoneNumberId,
          productsToSend,
          customer.system_access_token,
          3 // Max 3 images
        );

        if (imagesSent) {
          logger.debug(`Order processing: Product images sent | From: ${fromNumber} | Count: ${productsToSend.length}`);
        }
      } catch (imageError) {
        console.error("Error sending product images in order processing:", imageError);
        // Continue without images - text response was already sent
      }
    }

  } catch (error) {
    console.error("Error handling order processing:", error);
    await sendWhatsAppMessage(
      fromNumber,
      "Sorry, I encountered an error processing your order. Please try again.",
      phoneNumberId,
    );
  }
}

// Helper function to determine required fields from Google Sheets configuration
async function getRequiredFields(customer, whatsappConfig) {
  const requiredFields = [];
  let spreadsheetColumns = [];

  // Check both old and new Google Sheets systems for required fields
  console.log(`🔍 WhatsApp config spreadsheet columns:`, whatsappConfig.spreadsheet_columns);

  if (whatsappConfig.spreadsheet_columns && Array.isArray(whatsappConfig.spreadsheet_columns)) {
    spreadsheetColumns = whatsappConfig.spreadsheet_columns;
    console.log(`✅ Using WhatsApp config columns: ${JSON.stringify(spreadsheetColumns)}`);
  } else {
    // Try to get from new Google integrations system
    try {
      const { data: googleIntegration, error } = await supabase
        .from("google_integrations")
        .select("spreadsheet_columns")
        .eq("auth_id", customer.auth_id)
        .eq("service_type", "sheets")
        .eq("is_enabled", true)
        .single();

      if (!error && googleIntegration && googleIntegration.spreadsheet_columns) {
        spreadsheetColumns = googleIntegration.spreadsheet_columns;
        console.log(`✅ Using Google integrations columns: ${JSON.stringify(spreadsheetColumns)}`);
      } else {
        console.log(`❌ No Google integrations found or no columns configured`);
      }
    } catch (integrationError) {
      console.error("Error checking Google integrations for columns:", integrationError);
    }
  }

  // Determine which fields are required based on spreadsheet columns
  console.log(`🔍 Spreadsheet columns found: ${JSON.stringify(spreadsheetColumns)}`);

  if (spreadsheetColumns.length > 0) {
    spreadsheetColumns.forEach(column => {
      const lowerColumn = column.toLowerCase();
      console.log(`🔍 Checking column: "${column}" (lowercase: "${lowerColumn}")`);

      if (lowerColumn.includes('name') && !lowerColumn.includes('product') && !lowerColumn.includes('service')) {
        requiredFields.push('name');
        console.log(`✅ Added 'name' as required field`);
      }
      if (lowerColumn.includes('phone') || lowerColumn.includes('number')) {
        requiredFields.push('phone');
        console.log(`✅ Added 'phone' as required field`);
      }
      if (lowerColumn.includes('address') || lowerColumn.includes('location')) {
        requiredFields.push('address');
        console.log(`✅ Added 'address/location' as required field`);
      }
      if (lowerColumn.includes('email')) {
        requiredFields.push('email');
        console.log(`✅ Added 'email' as required field`);
      }
      if (lowerColumn.includes('date') || lowerColumn.includes('time') || lowerColumn.includes('appointment') || lowerColumn.includes('delivery')) {
        // Only add datetime if not already added as a different field
        if (!requiredFields.includes('datetime')) {
          requiredFields.push('datetime');
          console.log(`✅ Added 'datetime' as required field for column: ${column}`);
        }
      }
      // Note: Order ID is auto-generated, not a required field from customer
    });
  }

  // Default required fields if no spreadsheet configuration
  if (requiredFields.length === 0) {
    requiredFields.push('name', 'phone', 'address');
  }

  return requiredFields;
}

// Helper function to validate that all required fields are collected
async function validateRequiredFields(customer, fromNumber, existingOrder, whatsappConfig) {
  try {
    // Get required fields using the helper function
    const requiredFields = await getRequiredFields(customer, whatsappConfig);

    console.log(`🔍 Validating required fields: ${requiredFields.join(', ')}`);

    // Get current customer information from existing order
    const customerInfo = existingOrder?.customer_info || {};

    console.log(`📋 Current customer info in validation:`, JSON.stringify(customerInfo, null, 2));
    console.log(`📋 Existing order ID: ${existingOrder?.id}`);

    // Check which fields are missing
    const missingFields = [];

    requiredFields.forEach(field => {
      let hasField = false;

      switch(field) {
        case 'name':
          hasField = !!(customerInfo.name || customerInfo.customer_name || customerInfo['Customer Name']);
          console.log(`🔍 Validating 'name': ${hasField} | Available: name="${customerInfo.name}", customer_name="${customerInfo.customer_name}", Customer Name="${customerInfo['Customer Name']}"`);
          break;
        case 'phone':
          hasField = !!(customerInfo.phone || customerInfo.customer_phone || customerInfo['Customer Phone'] || fromNumber);
          console.log(`🔍 Validating 'phone': ${hasField} | Available: phone="${customerInfo.phone}", customer_phone="${customerInfo.customer_phone}", Customer Phone="${customerInfo['Customer Phone']}", fromNumber="${fromNumber}"`);
          break;
        case 'address':
          hasField = !!(customerInfo.address || customerInfo.customer_address || customerInfo['Customer Address'] || customerInfo.location);
          console.log(`🔍 Validating 'address': ${hasField} | Available: address="${customerInfo.address}", customer_address="${customerInfo.customer_address}", Customer Address="${customerInfo['Customer Address']}", location="${customerInfo.location}"`);
          break;
        case 'email':
          hasField = !!(customerInfo.email || customerInfo.customer_email || customerInfo['Customer Email']);
          console.log(`🔍 Validating 'email': ${hasField} | Available: email="${customerInfo.email}", customer_email="${customerInfo.customer_email}", Customer Email="${customerInfo['Customer Email']}"`);
          break;
        case 'datetime':
          hasField = !!(customerInfo.datetime || customerInfo.appointment_time || customerInfo['Date & Time'] || customerInfo.date || customerInfo.delivery_date);
          console.log(`🔍 Validating 'datetime': ${hasField} | Available: datetime="${customerInfo.datetime}", appointment_time="${customerInfo.appointment_time}", Date & Time="${customerInfo['Date & Time']}", date="${customerInfo.date}", delivery_date="${customerInfo.delivery_date}"`);
          break;
        default:
          hasField = !!(customerInfo[field] || customerInfo[field.toLowerCase()]);
          console.log(`🔍 Validating '${field}': ${hasField} | Available: ${field}="${customerInfo[field]}", ${field.toLowerCase()}="${customerInfo[field.toLowerCase()]}"`);
      }

      if (!hasField) {
        missingFields.push(field);
        console.log(`❌ Missing field: ${field}`);
      } else {
        console.log(`✅ Field satisfied: ${field}`);
      }
    });

    console.log(`📋 Customer info available:`, customerInfo);
    console.log(`❌ Missing fields: ${missingFields.length > 0 ? missingFields.join(', ') : 'None'}`);

    return missingFields;
  } catch (error) {
    console.error("Error validating required fields:", error);
    return []; // Return empty array on error to allow completion
  }
}

// Helper function to complete an order
async function completeOrder(customer, fromNumber, response, existingOrder, whatsappConfig) {
  try {
    let orderId = existingOrder?.id;
    const orderSummary = response.replace("ORDER_COMPLETE:", "").trim();

    // Get customer info from existing order or contact data
    let customerInfo = {};

    if (existingOrder && existingOrder.customer_info) {
      customerInfo = { ...existingOrder.customer_info };
    } else {
      // Get customer info from contact database
      const { data: contactData, error: contactError } = await supabase
        .from("contacts")
        .select("name, phone_number, notes")
        .eq("auth_id", customer.auth_id)
        .eq("phone_number", fromNumber)
        .single();

      if (!contactError && contactData) {
        customerInfo = {
          name: contactData.name,
          phone: contactData.phone_number,
          // Try to extract address from notes if available
          address: contactData.notes && contactData.notes.includes('Address:')
            ? contactData.notes.split('Address:')[1].split('\n')[0].trim()
            : ''
        };
      }
    }

    // Ensure we have at least phone number from WhatsApp
    if (!customerInfo.phone) {
      customerInfo.phone = fromNumber;
    }

    console.log(`📋 Customer info for order completion:`, customerInfo);

    // Generate user-friendly order ID
    const generateOrderId = () => {
      const date = new Date();
      const dateStr = date.toISOString().slice(2, 10).replace(/-/g, ''); // YYMMDD
      const timeStr = date.toTimeString().slice(0, 5).replace(':', ''); // HHMM
      const randomStr = Math.random().toString(36).substring(2, 5).toUpperCase(); // 3 random chars
      return `ORD${dateStr}${timeStr}${randomStr}`;
    };

    console.log(`📋 Generating user-friendly order ID for completion`);

    // If no existing order, create one
    if (!orderId) {
      const userFriendlyOrderId = generateOrderId();

      const { data: newOrder, error: createError } = await supabase
        .from("orders")
        .insert({
          auth_id: customer.auth_id,
          phone_number: fromNumber,
          order_status: "placed",
          customer_info: customerInfo,
          order_summary: orderSummary,
          user_order_id: userFriendlyOrderId, // Store user-friendly ID
        })
        .select()
        .single();

      if (createError) throw createError;
      orderId = newOrder.id;
      customerInfo.order_id = userFriendlyOrderId; // Add to customer info for Google Sheets
    }
    try {
      // Update existing order to placed
      const userFriendlyOrderId = generateOrderId();

      await supabase
        .from("orders")
        .update({
          order_status: "placed",
          customer_info: customerInfo,
          order_summary: orderSummary,
          user_order_id: userFriendlyOrderId, // Add user-friendly ID
        })
        .eq("id", orderId);

      customerInfo.order_id = userFriendlyOrderId; // Add to customer info for Google Sheets
    } catch (error) {
      console.error("Error completing order:", error);
    }

    // Parse order items and deduct stock
    const orderItems = parseOrderItemsFromSummary(orderSummary);
    if (orderItems.length > 0) {
      // Proceed with stock deduction (no validation needed)
      const stockResult = await deductStockForOrder(customer.auth_id, orderItems, orderId);

      // Stock deduction completed
    }

    // Save to Google Sheets
    await saveOrderToGoogleSheets(customer, whatsappConfig, fromNumber, orderSummary, orderId, customerInfo);

    // Update monthly statistics using comprehensive RPC function
    const now = new Date();
    await supabase.rpc("update_monthly_statistics", {
      p_auth_id: customer.auth_id,
      p_year: now.getFullYear(),
      p_month: now.getMonth() + 1,
    });

  } catch (error) {
    console.error("Error completing order:", error);
  }
}

// Helper function to cancel an order
async function cancelOrder(customer, fromNumber, existingOrder) {
  try {
    if (existingOrder) {
      await supabase
        .from("orders")
        .update({
          order_status: "cancelled",
        })
        .eq("id", existingOrder.id);
        
              // Update monthly statistics using comprehensive RPC function
        const now = new Date();
        await supabase.rpc("update_monthly_statistics", {
          p_auth_id: customer.auth_id,
          p_year: now.getFullYear(),
          p_month: now.getMonth() + 1,
        });
    }

    console.log(`❌ Order Cancelled | Customer: ${customer.auth_id} | From: ${fromNumber}`);
    
  } catch (error) {
    console.error("Error cancelling order:", error);
  }
}

// Helper function to progressively extract customer info from conversation
function extractCustomerInfoFromMessage(message, existingCustomerInfo = {}) {
  const updatedInfo = { ...existingCustomerInfo };

  // Extract name (look for patterns like "my name is", "I'm", "call me", etc.)
  const namePatterns = [
    /(?:my name is|i'm|i am|call me|nama saya|saya)\s+([a-zA-Z\s]+)/i,
    /^([a-zA-Z\s]{2,20})$/i // Simple name pattern for standalone names
  ];

  for (const pattern of namePatterns) {
    const nameMatch = message.match(pattern);
    if (nameMatch && nameMatch[1] && nameMatch[1].trim().length > 1) {
      const extractedName = nameMatch[1].trim();
      // Avoid capturing common words as names
      if (!['yes', 'no', 'ok', 'okay', 'thanks', 'thank you', 'hi', 'hello', 'good', 'morning', 'afternoon', 'evening'].includes(extractedName.toLowerCase())) {
        updatedInfo.name = extractedName;
        break;
      }
    }
  }

  // Extract phone number
  const phonePattern = /(?:phone|number|contact|tel)\s*:?\s*([0-9\-\+\s]{8,15})|^([0-9\-\+\s]{8,15})$/i;
  const phoneMatch = message.match(phonePattern);
  if (phoneMatch) {
    const phone = (phoneMatch[1] || phoneMatch[2]).replace(/[\s\-]/g, '');
    if (phone.length >= 8) {
      updatedInfo.phone = phone;
    }
  }

  // Extract address (look for address keywords or longer text with location indicators)
  const addressPatterns = [
    /(?:address|alamat|location|deliver to|send to)\s*:?\s*(.+)/i,
    /^(.+(?:jalan|road|street|avenue|taman|bandar|shah alam|kuala lumpur|kl|selangor|johor|penang|perak|sabah|sarawak|melaka|negeri sembilan|pahang|terengganu|kelantan|perlis|putrajaya|labuan).+)$/i
  ];

  for (const pattern of addressPatterns) {
    const addressMatch = message.match(pattern);
    if (addressMatch && addressMatch[1] && addressMatch[1].trim().length > 10) {
      updatedInfo.address = addressMatch[1].trim();
      break;
    }
  }

  // Extract delivery date (parse relative dates like "tomorrow", "today", specific dates)
  const datePatterns = [
    /(?:delivery date|deliver on|for|date)\s*:?\s*(tomorrow|today|next week|this week)/i,
    /(?:delivery date|deliver on|for|date)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/i,
    /(?:delivery date|deliver on|for|date)\s*:?\s*(\d{1,2}\s+(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\s*\d{2,4})/i,
    /^(tomorrow|today|next week|this week)$/i,
    /for\s+(tomorrow|today|next week|this week)/i,
    // Enhanced patterns for more complex date/time expressions
    /\b(\d+(?:st|nd|rd|th)\s+of\s+next\s+month(?:\s+at\s+\d+\s*pm|\s+at\s+\d+\s*am)?)\b/i,
    /\b(next\s+month\s+\d+(?:st|nd|rd|th)(?:\s+\d+\s*pm|\s+\d+\s*am)?)\b/i,
    /\b(tomorrow\s+at\s+\d+\s*pm|tomorrow\s+at\s+\d+\s*am|tomorrow\s+\d+\s*pm|tomorrow\s+\d+\s*am)\b/i,
    /\b(today\s+at\s+\d+\s*pm|today\s+at\s+\d+\s*am|today\s+\d+\s*pm|today\s+\d+\s*am)\b/i,
    /^(.+(?:pm|am|o'clock).*)$/i // Any message ending with time
  ];

  for (const pattern of datePatterns) {
    const dateMatch = message.match(pattern);
    if (dateMatch && dateMatch[1]) {
      const dateText = dateMatch[1].toLowerCase().trim();
      let deliveryDate = null;

      const today = new Date();
      if (dateText === 'today') {
        deliveryDate = today;
      } else if (dateText === 'tomorrow') {
        deliveryDate = new Date(today);
        deliveryDate.setDate(today.getDate() + 1);
      } else if (dateText === 'next week') {
        deliveryDate = new Date(today);
        deliveryDate.setDate(today.getDate() + 7);
      } else if (dateText === 'this week') {
        deliveryDate = new Date(today);
        deliveryDate.setDate(today.getDate() + 2); // Default to 2 days from now
      } else if (dateText.includes('next month')) {
        // Handle "3rd of next month" or similar
        deliveryDate = new Date(today);
        deliveryDate.setMonth(today.getMonth() + 1);
        // For now, just set to next month - could be enhanced to parse specific day
        deliveryDate.setDate(3); // Default to 3rd if mentioned
      } else {
        // Try to parse specific date formats
        deliveryDate = new Date(dateText);
        if (isNaN(deliveryDate.getTime())) {
          deliveryDate = null;
        }
      }

      if (deliveryDate && !isNaN(deliveryDate.getTime())) {
        const formattedDate = deliveryDate.toLocaleDateString();
        // Set multiple date field variations to ensure validation passes
        updatedInfo.delivery_date = formattedDate;
        updatedInfo.date = dateText; // Keep original text for display
        updatedInfo.datetime = dateText;
        updatedInfo.appointment_time = dateText;
        updatedInfo['Date & Time'] = dateText;
        console.log(`📅 Parsed delivery date: "${dateText}" -> ${formattedDate} | Set multiple date fields`);
        break;
      }
    }
  }

  return updatedInfo;
}

// Helper function to update or create order
async function updateOrCreateOrder(customer, fromNumber, message, existingOrder) {
  try {
    if (existingOrder) {
      // Extract any new customer information from the current message
      const extractedInfo = extractCustomerInfoFromMessage(message, existingOrder.customer_info || {});

      // Only update if we found new information
      if (JSON.stringify(extractedInfo) !== JSON.stringify(existingOrder.customer_info || {})) {
        await supabase
          .from("orders")
          .update({
            customer_info: extractedInfo,
            updated_at: new Date().toISOString()
          })
          .eq("id", existingOrder.id);

        console.log(`📝 Updated order ${existingOrder.id} with customer info:`, extractedInfo);
      }
    } else {
      // Extract customer info from the initial message
      const initialCustomerInfo = extractCustomerInfoFromMessage(message);

      // Pre-populate phone number from WhatsApp
      initialCustomerInfo.phone = fromNumber;

      // Try to get name from recent conversation history
      if (!initialCustomerInfo.name) {
        try {
          const { data: recentMessages, error: historyError } = await supabase
            .from("chat_history")
            .select("content, role")
            .eq("auth_id", customer.auth_id)
            .eq("phone_number", fromNumber)
            .order("created_at", { ascending: false })
            .limit(20);

          if (!historyError && recentMessages) {
            // Look for name in recent conversation
            for (const msg of recentMessages) {
              if (msg.role === 'user') {
                const extractedFromHistory = extractCustomerInfoFromMessage(msg.content);
                if (extractedFromHistory.name && !initialCustomerInfo.name) {
                  initialCustomerInfo.name = extractedFromHistory.name;
                  console.log(`📝 Found name from conversation history: ${extractedFromHistory.name}`);
                  break;
                }
              }
            }
          }
        } catch (historyError) {
          console.error("Error checking conversation history for name:", historyError);
        }
      }

      // Create new pending order
      const { data: newOrder, error: createError } = await supabase
        .from("orders")
        .insert({
          auth_id: customer.auth_id,
          phone_number: fromNumber,
          order_status: "pending",
          customer_info: initialCustomerInfo,
          order_summary: null,
        })
        .select()
        .single();

      if (createError) throw createError;

      console.log(`🆕 Created new order ${newOrder.id} with initial customer info:`, initialCustomerInfo);

      // Update monthly statistics using comprehensive RPC function
      const now = new Date();
      await supabase.rpc("update_monthly_statistics", {
        p_auth_id: customer.auth_id,
        p_year: now.getFullYear(),
        p_month: now.getMonth() + 1,
      });

    }
  } catch (error) {
    console.error("Error updating/creating order:", error);
  }
}

// Helper function to parse order summary and extract product items for stock deduction
function parseOrderItemsFromSummary(orderSummary) {
  const orderItems = [];

  try {
    // Common patterns for product information in order summaries
    const patterns = [
      // Pattern 1: "Product: Name, Quantity: X, Variation: Y"
      /(?:product|item)[:\s]*([^,\n]+)(?:,\s*(?:quantity|qty)[:\s]*([0-9]+))?(?:,\s*(?:variation|variant|size|color)[:\s]*([^,\n]+))?/gi,

      // Pattern 2: "X x Product Name (Variation)"
      /([0-9]+)\s*x\s*([^(\n]+)(?:\(([^)]+)\))?/gi,

      // Pattern 3: "Quantity: X, Product: Name, Variation: Y"
      /(?:quantity|qty)[:\s]*([0-9]+)(?:,\s*(?:product|item)[:\s]*([^,\n]+))?(?:,\s*(?:variation|variant|size|color)[:\s]*([^,\n]+))?/gi,

      // Pattern 4: Simple "Product Name - Quantity: X"
      /([^-\n]+)\s*-\s*(?:quantity|qty)[:\s]*([0-9]+)/gi,

      // Pattern 5: "Product Name (Variation) x Quantity"
      /([^(\n]+)(?:\(([^)]+)\))?\s*x\s*([0-9]+)/gi
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(orderSummary)) !== null) {
        let productName, quantity, variation;

        // Extract based on pattern type
        if (pattern.source.includes('product|item')) {
          // Pattern 1 or 3
          if (match[1] && /^[0-9]+$/.test(match[1])) {
            // Pattern 3: quantity first
            quantity = parseInt(match[1]);
            productName = match[2];
            variation = match[3];
          } else {
            // Pattern 1: product first
            productName = match[1];
            quantity = match[2] ? parseInt(match[2]) : 1;
            variation = match[3];
          }
        } else if (pattern.source.includes('[0-9]+.*x')) {
          // Pattern 2: "X x Product"
          quantity = parseInt(match[1]);
          productName = match[2];
          variation = match[3];
        } else if (pattern.source.includes('x.*[0-9]+')) {
          // Pattern 5: "Product x X"
          productName = match[1];
          variation = match[2];
          quantity = parseInt(match[3]);
        } else {
          // Pattern 4: "Product - Quantity: X"
          productName = match[1];
          quantity = parseInt(match[2]);
        }

        if (productName && quantity && quantity > 0) {
          // Clean up product name
          productName = productName.trim().replace(/[:\-,]+$/, '');

          // Check if this item already exists (avoid duplicates)
          const existingItem = orderItems.find(item =>
            item.productName.toLowerCase() === productName.toLowerCase() &&
            (item.variation || '').toLowerCase() === (variation || '').toLowerCase()
          );

          if (existingItem) {
            existingItem.quantity += quantity;
          } else {
            orderItems.push({
              productName: productName,
              quantity: quantity,
              variation: variation ? variation.trim() : null
            });
          }
        }
      }
    }

    // If no items found with patterns, try a fallback approach
    if (orderItems.length === 0) {
      // Look for any product names mentioned with quantities
      const fallbackPattern = /([^,\n]+)(?:.*?(?:quantity|qty|x)[:\s]*([0-9]+))/gi;
      let match;
      while ((match = fallbackPattern.exec(orderSummary)) !== null) {
        const productName = match[1].trim().replace(/^(product|item)[:\s]*/i, '');
        const quantity = parseInt(match[2]);

        if (productName && quantity > 0) {
          orderItems.push({
            productName: productName,
            quantity: quantity,
            variation: null
          });
        }
      }
    }

    return orderItems;

  } catch (error) {
    console.error("Error parsing order items from summary:", error);
    return [];
  }
}

// Helper function to extract customer info from order summary
function extractCustomerInfoFromSummary(orderSummary, spreadsheetColumns) {
  const customerInfo = {};

  for (const column of spreadsheetColumns) {
    const lowerColumn = column.toLowerCase();

    if (lowerColumn.includes('date') || lowerColumn.includes('time')) {
      // Extract date/time information from order summary
      const dateMatch = orderSummary.match(/(?:date|delivery|when|time)[:\s]*([^,\n\r]+)/i) ||
                       orderSummary.match(/\b(today|tomorrow|tonight|morning|afternoon|evening|\d+\s*pm|\d+\s*am|\d+:\d+|next\s+\w+)\b/i);
      customerInfo[column] = dateMatch ? dateMatch[1].trim() : new Date().toLocaleDateString();
    } else if (lowerColumn.includes('name')) {
      const nameMatch = orderSummary.match(/(?:name|customer)[:\s]+([^,\n\r]+)/i);
      customerInfo[column] = nameMatch ? nameMatch[1].trim() : '';
    } else if (lowerColumn.includes('phone') || lowerColumn.includes('number')) {
      const phoneMatch = orderSummary.match(/(?:phone|number|contact)[:\s]+([^,\n\r]+)/i);
      customerInfo[column] = phoneMatch ? phoneMatch[1].trim() : '';
    } else if (lowerColumn.includes('address')) {
      const addressMatch = orderSummary.match(/address[:\s]+([^,\n\r]+)/i);
      customerInfo[column] = addressMatch ? addressMatch[1].trim() : '';
    } else if (lowerColumn.includes('email')) {
      const emailMatch = orderSummary.match(/email[:\s]+([^,\n\r]+)/i);
      customerInfo[column] = emailMatch ? emailMatch[1].trim() : '';
    } else if (lowerColumn.includes('variation') || lowerColumn.includes('variant') || lowerColumn.includes('size') || lowerColumn.includes('color')) {
      // Extract variation/variant information
      const variationMatch = orderSummary.match(/(?:variation|variant|size|color)[:\s]+([^,\n\r]+)/i);
      customerInfo[column] = variationMatch ? variationMatch[1].trim() : '';
    } else if (lowerColumn.includes('item') || lowerColumn.includes('product')) {
      // Extract product details more intelligently
      const productMatch = orderSummary.match(/(?:item|product)[:\s]+([^,\n\r]+)/i);
      customerInfo[column] = productMatch ? productMatch[1].trim() : '';
    } else if (lowerColumn.includes('quantity') || lowerColumn.includes('qty')) {
      const qtyMatch = orderSummary.match(/(?:quantity|qty|amount)[:\s]+([0-9]+)/i);
      customerInfo[column] = qtyMatch ? qtyMatch[1].trim() : '';
    } else if (lowerColumn.includes('total') || lowerColumn.includes('price') || lowerColumn.includes('amount')) {
      const priceMatch = orderSummary.match(/(?:total|price|amount)[:\s]+(?:rm|$)?\s*([0-9]+\.?[0-9]*)/i);
      customerInfo[column] = priceMatch ? `RM${priceMatch[1]}` : '';
    } else if (lowerColumn.includes('status')) {
      // For status columns, default to 'Placed'
      customerInfo[column] = 'Placed';
    } else if (lowerColumn.includes('id')) {
      // ID columns will be handled separately in the saveOrderToGoogleSheets function
      customerInfo[column] = '';
    } else {
      // Try to extract any field that matches the column name
      const fieldMatch = orderSummary.match(new RegExp(`${column}[:\\s]+([^,\\n\\r]+)`, 'i'));
      customerInfo[column] = fieldMatch ? fieldMatch[1].trim() : '';
    }
  }

  // Log extracted customer info for debugging
  console.log(`📋 Extracted customer info:`, customerInfo);

  return customerInfo;
}

// Helper function to deduct stock quantities for order items
async function deductStockForOrder(authId, orderItems, orderId) {
  const stockDeductions = [];
  const errors = [];

  try {
    for (const orderItem of orderItems) {
      try {
        // Find matching product in catalog
        let query = supabase
          .from("product_catalog")
          .select("id, name, stock_quantity, is_service, variations")
          .eq("auth_id", authId)
          .eq("is_active", true);

        // Try exact name match first
        const { data: exactMatches, error: exactError } = await query
          .ilike("name", orderItem.productName);

        if (exactError) {
          console.error(`Error finding exact match for ${orderItem.productName}:`, exactError);
          continue;
        }

        let matchedProduct = null;

        if (exactMatches && exactMatches.length > 0) {
          matchedProduct = exactMatches[0];
        } else {
          // Try fuzzy matching if exact match fails
          const { data: allProducts, error: allError } = await supabase
            .from("product_catalog")
            .select("id, name, stock_quantity, is_service, variations")
            .eq("auth_id", authId)
            .eq("is_active", true);

          if (allError) {
            console.error(`Error fetching products for fuzzy matching:`, allError);
            continue;
          }

          // Find best match using simple string similarity
          const productName = orderItem.productName.toLowerCase();
          let bestMatch = null;
          let bestScore = 0;

          for (const product of allProducts) {
            const catalogName = product.name.toLowerCase();

            // Check if product name contains the order item name or vice versa
            if (catalogName.includes(productName) || productName.includes(catalogName)) {
              const score = Math.max(
                productName.length / catalogName.length,
                catalogName.length / productName.length
              );

              if (score > bestScore && score > 0.5) {
                bestScore = score;
                bestMatch = product;
              }
            }
          }

          matchedProduct = bestMatch;
        }

        if (!matchedProduct) {
          errors.push(`Product not found: ${orderItem.productName}`);
          console.warn(`⚠️ Product not found in catalog: ${orderItem.productName}`);
          continue;
        }

        // Skip stock deduction for services
        if (matchedProduct.is_service) {
          console.log(`🔧 Skipping stock deduction for service: ${matchedProduct.name}`);
          stockDeductions.push({
            productId: matchedProduct.id,
            productName: matchedProduct.name,
            requestedQuantity: orderItem.quantity,
            deductedQuantity: 0,
            newStockQuantity: matchedProduct.stock_quantity,
            isService: true,
            status: 'skipped'
          });
          continue;
        }

        // Calculate new stock quantity (minimum 0)
        const newStockQuantity = Math.max(0, matchedProduct.stock_quantity - orderItem.quantity);
        const actualDeducted = matchedProduct.stock_quantity - newStockQuantity;

        // Update stock in database
        const { data: updatedProduct, error: updateError } = await supabase
          .from("product_catalog")
          .update({
            stock_quantity: newStockQuantity,
            updated_at: new Date().toISOString()
          })
          .eq("id", matchedProduct.id)
          .eq("auth_id", authId)
          .select("id, name, stock_quantity")
          .single();

        if (updateError) {
          const errorMsg = `Failed to update stock for ${matchedProduct.name}: ${updateError.message}`;
          errors.push(errorMsg);
          console.error(`❌ ${errorMsg}`);
          continue;
        }

        // Record successful deduction
        stockDeductions.push({
          productId: matchedProduct.id,
          productName: matchedProduct.name,
          requestedQuantity: orderItem.quantity,
          deductedQuantity: actualDeducted,
          previousStockQuantity: matchedProduct.stock_quantity,
          newStockQuantity: newStockQuantity,
          isService: false,
          status: 'success'
        });

        // Stock deduction completed

      } catch (itemError) {
        const errorMsg = `Error processing ${orderItem.productName}: ${itemError.message}`;
        errors.push(errorMsg);
        console.error(`❌ ${errorMsg}`);
      }
    }

    // Log summary
    const successfulDeductions = stockDeductions.filter(d => d.status === 'success').length;
    const skippedServices = stockDeductions.filter(d => d.status === 'skipped').length;
    const failedDeductions = stockDeductions.filter(d => d.status === 'insufficient_stock').length;

    console.log(`📊 Stock deduction summary for order ${orderId}:`);
    console.log(`   ✅ Successful: ${successfulDeductions}`);
    console.log(`   🔧 Services (skipped): ${skippedServices}`);
    console.log(`   ❌ Failed: ${failedDeductions}`);
    console.log(`   ⚠️ Errors: ${errors.length}`);

    return {
      success: errors.length === 0,
      stockDeductions: stockDeductions,
      errors: errors,
      summary: {
        totalItems: orderItems.length,
        successfulDeductions: successfulDeductions,
        skippedServices: skippedServices,
        failedDeductions: failedDeductions,
        errorCount: errors.length
      }
    };

  } catch (error) {
    console.error(`❌ Critical error in stock deduction for order ${orderId}:`, error);
    return {
      success: false,
      stockDeductions: stockDeductions,
      errors: [`Critical error: ${error.message}`],
      summary: {
        totalItems: orderItems.length,
        successfulDeductions: 0,
        skippedServices: 0,
        failedDeductions: 0,
        errorCount: 1
      }
    };
  }
}



// Note: Monthly order statistics are now handled by the update_monthly_statistics RPC function

// Helper function to save order to Google Sheets
async function saveOrderToGoogleSheets(customer, whatsappConfig, fromNumber, orderSummary, orderId, customerInfo = {}) {
  try {
    // Check both old system (whatsappConfig) and new system (google_integrations)
    let spreadsheetConfig = null;

    // First, try the old system (whatsapp_customers table)
    if (whatsappConfig.spreadsheet_id) {
      spreadsheetConfig = {
        spreadsheet_id: whatsappConfig.spreadsheet_id,
        worksheet_name: whatsappConfig.worksheet_name || 'Data',
        spreadsheet_columns: whatsappConfig.spreadsheet_columns || ['OrderID', 'Date','Time', 'Customer Phone', 'Customer Name', 'Address', 'Product', 'Variation', 'Quantity', 'Total Amount', 'Status'],
        system: 'old'
      };
    } else {
      // Try the new system (google_integrations table)
      try {
        const { data: googleIntegration, error } = await supabase
          .from("google_integrations")
          .select("*")
          .eq("auth_id", customer.auth_id)
          .eq("service_type", "sheets")
          .eq("is_enabled", true)
          .single();

        if (!error && googleIntegration && googleIntegration.spreadsheet_id) {
          spreadsheetConfig = {
            spreadsheet_id: googleIntegration.spreadsheet_id,
            worksheet_name: googleIntegration.worksheet_name || 'Data',
            spreadsheet_columns: googleIntegration.spreadsheet_columns || ['OrderID', 'Date','Time', 'Customer Phone', 'Customer Name', 'Address', 'Product', 'Variation', 'Quantity', 'Total Amount', 'Status'],
            system: 'new'
          };
        }
      } catch (integrationError) {
        console.error("Error checking Google integrations:", integrationError);
      }
    }

    if (!spreadsheetConfig) {
      console.log("No spreadsheet configured in either system, skipping Google Sheets save");
      return;
    }

    console.log(`📊 Using ${spreadsheetConfig.system} Google Sheets system | Spreadsheet ID: ${spreadsheetConfig.spreadsheet_id}`);

    // Prepare row data based on spreadsheet columns
    const rowData = [];
    const columns = spreadsheetConfig.spreadsheet_columns;

    for (const column of columns) {
      const lowerColumn = column.toLowerCase();

      // First, try to use the extracted customer info if available
      if (customerInfo[column]) {
        rowData.push(customerInfo[column]);
      } else if (lowerColumn.includes('date') || lowerColumn.includes('time')) {
        rowData.push(new Date().toLocaleDateString());
      } else if (lowerColumn.includes('phone') || lowerColumn.includes('number')) {
        rowData.push(fromNumber);
      } else if (lowerColumn.includes('status')) {
        rowData.push('Placed');
      } else if (lowerColumn.includes('id') || lowerColumn.includes('order')) {
        // Use user-friendly order ID if available, otherwise use database ID, or row ID as final fallback
        let orderIdToUse = customerInfo.order_id || `ORD${orderId}`;

        // If this is being saved to Google Sheets and no order ID exists,
        // the row ID will be generated by Google Sheets automatically
        console.log(`📊 Setting Order ID for Google Sheets: ${orderIdToUse}`);
        rowData.push(orderIdToUse);
      } else if (lowerColumn.includes('customer') || lowerColumn.includes('name')) {
        // Use collected customer info first, then try to extract from order summary
        let customerName = '';
        if (customerInfo.name || customerInfo.customer_name || customerInfo['Customer Name']) {
          customerName = customerInfo.name || customerInfo.customer_name || customerInfo['Customer Name'];
        } else {
          const nameMatch = orderSummary.match(/(?:customer\s*name|name)[:\s]+([^,\n]+)/i);
          customerName = nameMatch ? nameMatch[1].trim() : 'N/A';
        }
        rowData.push(customerName);
      } else if (lowerColumn.includes('address') || lowerColumn.includes('delivery') || lowerColumn.includes('location')) {
        // Use collected customer info first, then try to extract from order summary
        let address = '';
        if (customerInfo.address || customerInfo.customer_address || customerInfo['Customer Address'] || customerInfo.location) {
          address = customerInfo.address || customerInfo.customer_address || customerInfo['Customer Address'] || customerInfo.location;
        } else {
          const addressMatch = orderSummary.match(/(?:address|delivery|location)[:\s]+([^,\n]+)/i);
          address = addressMatch ? addressMatch[1].trim() : '';
        }
        rowData.push(address);
      } else if (lowerColumn.includes('date') || lowerColumn.includes('time') || lowerColumn.includes('appointment')) {
        // Extract date/time information for bookings
        let datetime = '';
        if (customerInfo.datetime || customerInfo.appointment_time || customerInfo['Date & Time'] || customerInfo.date) {
          datetime = customerInfo.datetime || customerInfo.appointment_time || customerInfo['Date & Time'] || customerInfo.date;
        } else {
          const datetimeMatch = orderSummary.match(/(?:date|time|appointment)[:\s]+([^,\n]+)/i);
          datetime = datetimeMatch ? datetimeMatch[1].trim() : '';
        }
        rowData.push(datetime);
      } else if (lowerColumn.includes('service') || lowerColumn.includes('booking')) {
        // Extract service information for bookings
        let serviceInfo = '';
        const servicePatterns = [
          /(?:service|booking)[:\s]*([^,\n]+)/i,
          /([^-\n]+?)\s*booking/i,
          /appointment\s+for\s+([^,\n]+)/i
        ];

        for (const pattern of servicePatterns) {
          const match = orderSummary.match(pattern);
          if (match) {
            serviceInfo = match[1].trim();
            break;
          }
        }
        rowData.push(serviceInfo || 'Service booking');
      } else if (lowerColumn.includes('total') || lowerColumn.includes('amount') || lowerColumn.includes('price')) {
        // Extract total amount - try multiple patterns
        let totalAmount = '';

        // Try to extract from order items first
        const orderItems = parseOrderItemsFromSummary(orderSummary);
        if (orderItems.length > 0) {
          const calculatedTotal = orderItems.reduce((sum, item) => {
            const price = parseFloat(item.price) || 0;
            return sum + (price * item.quantity);
          }, 0);
          if (calculatedTotal > 0) {
            totalAmount = `RM${calculatedTotal.toFixed(2)}`;
          }
        }

        // Fallback to regex extraction
        if (!totalAmount) {
          const totalMatch = orderSummary.match(/(?:total|amount|price)[:\s]*(?:rm\s*)?([0-9.]+)/i) ||
                            orderSummary.match(/rm\s*([0-9.]+)/i) ||
                            orderSummary.match(/([0-9.]+)\s*(?:rm|ringgit)/i);
          totalAmount = totalMatch ? `RM${totalMatch[1]}` : '';
        }

        rowData.push(totalAmount);
      } else if (lowerColumn.includes('quantity') || lowerColumn.includes('qty')) {
        // Extract quantity from order summary
        const orderItems = parseOrderItemsFromSummary(orderSummary);
        if (orderItems.length > 0) {
          const totalQuantity = orderItems.reduce((sum, item) => sum + item.quantity, 0);
          rowData.push(totalQuantity.toString());
        } else {
          const qtyMatch = orderSummary.match(/(?:quantity|qty)[:\s]*([0-9]+)/i);
          rowData.push(qtyMatch ? qtyMatch[1] : '1');
        }
      } else if (lowerColumn.includes('variation') || lowerColumn.includes('variant') || lowerColumn.includes('size') || lowerColumn.includes('color')) {
        // Extract variation information
        const orderItems = parseOrderItemsFromSummary(orderSummary);
        if (orderItems.length > 0 && orderItems[0].variation) {
          rowData.push(orderItems[0].variation);
        } else {
          const variationMatch = orderSummary.match(/(?:variation|variant|size|color)[:\s]*([^,\n]+)/i);
          rowData.push(variationMatch ? variationMatch[1].trim() : '');
        }
      } else if (lowerColumn.includes('order') || lowerColumn.includes('item') || lowerColumn.includes('product')) {
        // Extract product information with multiple fallback patterns
        let productInfo = '';

        // Try parsing with the function first
        const orderItems = parseOrderItemsFromSummary(orderSummary);
        if (orderItems.length > 0) {
          const itemsList = orderItems.map(item =>
            `${item.quantity}x ${item.productName}${item.variation ? ` (${item.variation})` : ''}`
          ).join(', ');
          productInfo = itemsList;
        } else {
          // Fallback patterns for different order summary formats
          const patterns = [
            // Pattern: "2 Chocolate Muffins for RM18"
            /(\d+)\s+([^-\n]+?)\s+for\s+RM\d+/i,
            // Pattern: "Product: 2 Chocolate Muffins - RM18"
            /(?:product|item)[:\s]*(\d+\s+[^-\n]+)/i,
            // Pattern: "2x Chocolate Muffins"
            /(\d+x?\s*[^-\n,]+)/i,
            // Pattern: "Chocolate Muffins - RM18"
            /([^-\n]+?)\s*-\s*RM\d+/i,
            // Pattern: any text before "for RM" or "- RM"
            /([^-\n]+?)(?:\s+for\s+RM|\s*-\s*RM)/i
          ];

          for (const pattern of patterns) {
            const match = orderSummary.match(pattern);
            if (match) {
              productInfo = match[1].trim();
              break;
            }
          }

          // Final fallback - extract first meaningful line
          if (!productInfo) {
            const lines = orderSummary.split('\n').filter(line => line.trim());
            productInfo = lines.find(line =>
              !line.toLowerCase().includes('customer') &&
              !line.toLowerCase().includes('phone') &&
              !line.toLowerCase().includes('address') &&
              line.trim().length > 3
            ) || 'Order details not parsed';
          }
        }

        rowData.push(productInfo);
      } else {
        // Try to extract relevant information from order summary as fallback
        const fieldMatch = orderSummary.match(new RegExp(`${column}[:\\s]+([^,\\n]+)`, 'i'));
        rowData.push(fieldMatch ? fieldMatch[1].trim() : '');
      }
    }

    // Log the structured data for debugging
    console.log(`📊 Preparing Google Sheets data for Order ${orderId}:`);
    console.log(`   Order Summary: "${orderSummary}"`);
    console.log(`   Customer Info: ${JSON.stringify(customerInfo)}`);
    console.log(`   Columns: ${columns.join(', ')}`);
    console.log(`   Row Data: [${rowData.map(item => `"${item}"`).join(', ')}]`);

    // Save to Google Sheets using the appropriate system
    let sheetResult;
    if (spreadsheetConfig.system === 'new') {
      // Use new Google integrations system
      const { saveToGoogleSheetsNew } = await import('../utils/googleIntegrations.js');
      sheetResult = await saveToGoogleSheetsNew(customer.auth_id, rowData);
    } else {
      // Use old system with custom config
      const customConfig = {
        spreadsheet_id: spreadsheetConfig.spreadsheet_id,
        worksheet_name: spreadsheetConfig.worksheet_name,
        spreadsheet_columns: spreadsheetConfig.spreadsheet_columns,
        auth_id: customer.auth_id
      };
      const { saveToGoogleSheets } = await import('../utils/googleSheets.js');
      sheetResult = await saveToGoogleSheets(customConfig, rowData);
    }

    // Update order with spreadsheet row number
    if (sheetResult && sheetResult.updates && sheetResult.updates.updatedRows) {
      await supabase
        .from("orders")
        .update({
          spreadsheet_row_number: sheetResult.updates.updatedRows,
        })
        .eq("id", orderId);
    }

    console.log(`✅ Order saved to Google Sheets using ${spreadsheetConfig.system} system | Order: ${orderId} | Row: ${sheetResult?.updates?.updatedRows || 'Unknown'}`);

  } catch (error) {
    console.error(`❌ Error saving order to Google Sheets (${spreadsheetConfig?.system || 'unknown'} system):`, error);
    // Don't throw the error - we don't want to break order completion if Google Sheets fails
  }
}

// ===== WHATSAPP HELPER FUNCTIONS =====

// Helper function to download media from WhatsApp
async function downloadWhatsAppMedia(mediaId, accessToken) {
  try {
    logger.debug(`Downloading WhatsApp media | ID: ${mediaId}`);
    
    // First, get the media URL and info
    const mediaInfoResponse = await axios.get(
      `https://graph.facebook.com/v18.0/${mediaId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        timeout: 10000,
      },
    );

    const mediaUrl = mediaInfoResponse.data.url;
    const mimeType = mediaInfoResponse.data.mime_type;
    const fileSize = mediaInfoResponse.data.file_size;

    logger.debug(`Media info retrieved | Type: ${mimeType} | Size: ${fileSize} bytes`);

    // Download the actual media file
    const mediaResponse = await axios.get(mediaUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Accept': '*/*',
        'User-Agent': 'WhatsApp/2.24.1.84'
      },
      responseType: 'arraybuffer',
      timeout: 30000, // Longer timeout for media download
      maxContentLength: 16 * 1024 * 1024, // 16MB limit
    });

    const downloadedSize = mediaResponse.data.length;
    if (downloadedSize !== fileSize) {
      logger.warn(`Downloaded size (${downloadedSize} bytes) differs from reported size (${fileSize} bytes)`);
    }

    logger.debug(`Media downloaded successfully | Size: ${downloadedSize} bytes`);

    return {
      buffer: Buffer.from(mediaResponse.data),
      mimeType: mimeType,
      fileSize: downloadedSize,
    };
  } catch (error) {
    console.error("Error downloading WhatsApp media:", error);
    throw error;
  }
}

// Helper function to process image using OpenAI Vision
async function processImageWithAI(imageBuffer, mimeType) {
  try {
    console.log(`🔍 Processing image | Buffer size: ${imageBuffer.length} bytes | MIME: ${mimeType}`);

    // Convert buffer to base64
    const base64Image = imageBuffer.toString('base64');
    const dataUrl = `data:${mimeType};base64,${base64Image}`;

    console.log(`🔍 Image converted to base64 | Data URL length: ${dataUrl.length} chars`);

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Please describe what you see in this image. If this appears to be related to a product inquiry, order, or business question, provide a detailed description that would help a customer service AI understand the customer's intent. If there's any text in the image, please transcribe it as well.",
            },
            {
              type: "image_url",
              image_url: {
                url: dataUrl,
              },
            },
          ],
        },
      ],
      max_tokens: 1000,
    });

    const description = response.choices[0].message.content;
    console.log(`✅ OpenAI image analysis complete | Description: "${description.substring(0, 100)}..."`);
    return description;
  } catch (error) {
    console.error("❌ Error processing image with AI:", error);
    throw error;
  }
}


// Helper function to save WhatsApp media for chat history
async function saveWhatsAppMediaForHistory(mediaData, messageType, authId, phoneNumber) {
  try {
    // Validate buffer
    if (!mediaData.buffer || mediaData.buffer.length === 0) {
      console.error("Empty buffer received for WhatsApp media");
      return null;
    }

    // Validate file size
    if (!mediaData.fileSize || mediaData.fileSize === 0) {
      console.error("Invalid file size for WhatsApp media");
      return null;
    }

    // Generate unique filename
    const timestamp = Date.now();
    const random = Math.round(Math.random() * 1E9);
    let extension = '.bin';
    
    // Determine extension based on mime type
    if (mediaData.mimeType.includes('jpeg') || mediaData.mimeType.includes('jpg')) extension = '.jpg';
    else if (mediaData.mimeType.includes('png')) extension = '.png';
    else if (mediaData.mimeType.includes('gif')) extension = '.gif';
    else if (mediaData.mimeType.includes('webp')) extension = '.webp';
    else if (mediaData.mimeType.includes('mp3')) extension = '.mp3';
    else if (mediaData.mimeType.includes('wav')) extension = '.wav';
    else if (mediaData.mimeType.includes('ogg')) extension = '.ogg';
    else if (mediaData.mimeType.includes('m4a')) extension = '.m4a';
    else if (mediaData.mimeType.includes('aac')) extension = '.aac';
    else if (mediaData.mimeType.includes('mp4')) extension = '.mp4';
    else if (mediaData.mimeType.includes('webm')) extension = '.webm';
    
    const filename = `whatsapp-${messageType}-${timestamp}-${random}${extension}`;
    
    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase
      .storage
      .from('whatsapp-media')
      .upload(`${authId}/${filename}`, mediaData.buffer, {
        contentType: mediaData.mimeType,
        cacheControl: '31536000',
        upsert: false
      });

    if (uploadError) {
      console.error("Error uploading to Supabase Storage:", uploadError);
      return null;
    }

    // Get public URL
    const { data: { publicUrl } } = supabase
      .storage
      .from('whatsapp-media')
      .getPublicUrl(`${authId}/${filename}`);
    
    // Store file info in database
    const { data: savedMedia, error } = await supabase.from("uploaded_media").insert([
      {
        auth_id: authId,
        filename: filename,
        original_name: `whatsapp-${messageType}${extension}`,
        media_type: messageType,
        mime_type: mediaData.mimeType,
        file_size: mediaData.fileSize,
        file_url: publicUrl,
        storage_path: `${authId}/${filename}`,
        uploaded_at: new Date().toISOString(),
        source: 'whatsapp_incoming',
        phone_number: phoneNumber,
      },
    ]).select().single();
    
    if (error) {
      console.error("Error saving media info to database:", error);
      return null;
    }
    
    return {
      filename: filename,
      fileUrl: publicUrl,
      mediaType: messageType,
      mimeType: mediaData.mimeType,
      fileSize: mediaData.fileSize,
      storagePath: `${authId}/${filename}`,
      savedMedia: savedMedia
    };
    
  } catch (error) {
    console.error("Error saving WhatsApp media for history:", error);
    return null;
  }
}

// Helper function to process media content (now using enhanced version)
async function processMediaContent(messageContent, messageType, accessToken, authId = null, phoneNumber = null) {
  try {
    const result = await processMediaContentEnhanced(messageContent, messageType, accessToken, authId, phoneNumber);
    return {
      processedText: result.processedText,
      mediaInfo: result.mediaInfo
    };
  } catch (error) {
    console.error("Error in processMediaContent wrapper:", error);
    throw error;
  }
}


// Helper function to verify WhatsApp credentials
async function verifyWhatsAppCredentials(phoneNumberId, accessToken) {
  try {
    if (!accessToken) {
      console.error("No access token provided");
      return false;
    }

    const response = await axios.get(
      `https://graph.facebook.com/v18.0/${phoneNumberId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
    return response.status === 200;
  } catch (error) {
    console.error("Error verifying WhatsApp credentials:", error);

    // Check if token is expired
    if (
      error.response?.status === 401 ||
      error.response?.data?.error?.code === 190
    ) {
      // Token is expired or invalid
      console.error("WhatsApp access token is expired or invalid");
    }

    return false;
  }
}

// Helper function to process incoming messages
async function processIncomingMessage(
  customer,
  fromNumber,
  messageContent,
  phoneNumberId,
  messageType = 'text',
  platform = 'whatsapp'
) {
  const processingStartTime = Date.now();
  try {
    // First, process media content if it's not a text message
    let processedMessage = messageContent;
    let mediaResult = null; // Initialize mediaResult outside the if block
    
    if (messageType !== 'text') {
      console.log(`🎬 Starting media processing | Type: ${messageType} | Platform: ${platform}`);
      console.log(`🎬 Media content:`, messageContent);
      console.log(`🎬 Is document attachment: ${!!messageContent.filename}`);

      // Get customer's access token for media download based on platform
      let accessToken = null;

      if (platform === 'whatsapp') {
        // Try new social media integrations table first, then fallback to old table
        let customerConfig = await getPlatformIntegration(customer.auth_id, 'whatsapp');
        if (!customerConfig) {
          const { data: oldConfig } = await supabase
            .from("whatsapp_customers")
            .select("system_access_token")
            .eq("auth_id", customer.auth_id)
            .single();
          accessToken = oldConfig?.system_access_token;
        } else {
          accessToken = customerConfig.system_access_token;
        }
      } else if (platform === 'messenger') {
        const customerConfig = await getPlatformIntegration(customer.auth_id, 'messenger');
        accessToken = customerConfig?.messenger_access_token;
      } else if (platform === 'instagram') {
        const customerConfig = await getPlatformIntegration(customer.auth_id, 'instagram');
        accessToken = customerConfig?.instagram_access_token;
      }

      console.log(`🔑 Access token retrieved: ${accessToken ? 'YES' : 'NO'}`);

      if (!accessToken) {
        console.error(`❌ Error getting customer access token for ${platform} media`);
        // Send error message using appropriate platform
        if (platform === 'whatsapp') {
          await sendWhatsAppMessage(
            fromNumber,
            "Sorry, I'm having trouble processing your media message. Please try sending a text message instead.",
            phoneNumberId,
          );
        } else if (platform === 'messenger') {
          await sendMessengerTextMessage(
            fromNumber,
            "Sorry, I'm having trouble processing your media message. Please try sending a text message instead.",
            accessToken
          );
        } else if (platform === 'instagram') {
          await sendInstagramTextMessage(
            fromNumber,
            "Sorry, I'm having trouble processing your media message. Please try sending a text message instead.",
            accessToken
          );
        }
        return;
      }

      // Process the media content with enhanced processing
      console.log(`🎬 Calling processMediaContentEnhanced...`);
      mediaResult = await processMediaContentEnhanced(
        messageContent,
        messageType,
        accessToken,
        customer.auth_id,
        fromNumber
      );

      console.log(`🎬 Media processing result:`, {
        processedText: mediaResult?.processedText?.substring(0, 100) + '...',
        hasRawTranscription: !!mediaResult?.rawTranscription,
        hasMediaInfo: !!mediaResult?.mediaInfo,
        error: mediaResult?.error
      });

      processedMessage = mediaResult.processedText;
      
      logger.debug(`Media processed | Type: ${messageType} | Result length: ${processedMessage.length}`);
      if (mediaResult.rawTranscription) {
        logger.debug(`Raw transcription extracted for KB search: "${mediaResult.rawTranscription.substring(0, 50)}..."`);
      }
    } else {
      console.log(`📝 Text message processing | Content: "${messageContent.substring(0, 50)}..."`);
    }

    // Check user quotas before processing
    const { data: quotaCheck, error: quotaError } = await supabase.rpc(
      "check_user_quotas",
      { p_auth_id: customer.auth_id },
    );

    if (quotaError) {
      console.error("Error checking quotas:", quotaError);
      return;
    }

    const quotaInfo = quotaCheck[0];

    // If no active subscription or subscription expired
    if (!quotaInfo || !quotaInfo.subscription_active) {
      const errorMessage =
        quotaInfo?.subscription_status === "expired"
          ? "Your subscription has expired. Please renew to continue using the chatbot."
          : "No active subscription found. Please subscribe to use the chatbot.";

      // Log quota violation
      await supabase.from("quota_violations").insert({
        auth_id: customer.auth_id,
        violation_type: "subscription_expired",
        attempted_action: "whatsapp_message",
        current_usage: quotaInfo?.current_message_count || 0,
        limit_value: quotaInfo?.message_limit || 0,
        plan_name: "Unknown",
      });
      return;
    }

    // Check if user can send messages
    if (!quotaInfo.can_send_message) {
      const errorMessage = `You have reached your monthly message limit (${quotaInfo.current_message_count}/${quotaInfo.message_limit}). Please upgrade your plan or wait for next month.`;
      await sendWhatsAppMessage(fromNumber, errorMessage, phoneNumberId);

      // Log quota violation
      await supabase.from("quota_violations").insert({
        auth_id: customer.auth_id,
        violation_type: "message_limit",
        attempted_action: "whatsapp_message",
        current_usage: quotaInfo.current_message_count,
        limit_value: quotaInfo.message_limit,
        plan_name: "Current Plan",
      });
      return;
    }

    // Get chatbot settings to check if order processing is enabled
    const { data: settings, error: settingsError } = await supabase
      .from("chatbot_settings")
      .select("order_processing_enabled")
      .eq("auth_id", customer.auth_id)
      .single();

    // Check if user has a paid plan for order processing
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("plan")
      .eq("auth_id", customer.auth_id)
      .single();

    const isPaidPlan = profile && !['free', 'trial'].includes(profile.plan);
    const orderProcessingEnabled = settings?.order_processing_enabled && isPaidPlan;

    // Extract raw transcription for better knowledge base search
    const rawTranscription = (messageType !== 'text' && mediaResult?.rawTranscription) ? mediaResult.rawTranscription : null;
    
    // Check if there's an active human takeover session for this customer
    const { data: humanTakeoverCheck, error: humanTakeoverError } = await supabase.rpc(
      "is_human_takeover_active",
      { p_auth_id: customer.auth_id, p_phone_number: fromNumber }
    );

    if (humanTakeoverError) {
      console.error("Error checking human takeover session:", humanTakeoverError);
    }

    // If human takeover is active, don't generate AI response
    if (humanTakeoverCheck) {
      logger.info(`Human takeover active | From: ${fromNumber} | Skipping AI response`);
      
      // Store the customer message in chat history but don't generate AI response
      await supabase.from("chat_history").insert({
        auth_id: customer.auth_id,
        phone_number: fromNumber,
        role: "user",
        content: processedMessage,
      });
      
      return;
    }
    
    // Simple logic: if order processing enabled, use order mode, else regular chat
    if (orderProcessingEnabled) {
      // Check if there's an ongoing order for this contact
      const { data: existingOrder, error: orderError } = await supabase
        .from("orders")
        .select("*")
        .eq("auth_id", customer.auth_id)
        .eq("phone_number", fromNumber)
        .eq("order_status", "pending")
        .order("created_at", { ascending: false })
        .limit(1);

      if (orderError) {
        console.error("Error checking existing orders:", orderError);
      }

      logger.info(`Order mode processing | From: ${fromNumber}`);
      await handleOrderProcessing(customer, fromNumber, processedMessage, phoneNumberId, existingOrder?.[0] || null, rawTranscription);
    } else {
      logger.info(`Regular chat processing | From: ${fromNumber}`);
      await handleRegularChat(customer, fromNumber, processedMessage, phoneNumberId, rawTranscription);
    }

    // Store media information in chat history if media was processed
    if (messageType !== 'text' && typeof mediaResult !== 'undefined' && mediaResult.mediaInfo) {
      try {
        // Update the most recent chat history entry with media information
        const { error: updateError } = await supabase
          .from("chat_history")
          .update({
            media_filename: mediaResult.mediaInfo.filename,
            media_url: mediaResult.mediaInfo.fileUrl,
            media_type: mediaResult.mediaInfo.mediaType,
            mime_type: mediaResult.mediaInfo.mimeType,
            file_size: mediaResult.mediaInfo.fileSize
          })
          .eq("auth_id", customer.auth_id)
          .eq("phone_number", fromNumber)
          .eq("role", "user")
          .order("created_at", { ascending: false })
          .limit(1);

        if (updateError) {
          console.error("Error updating chat history with media info:", updateError);
        }
      } catch (error) {
        console.error("Error storing media info in chat history:", error);
      }
    }

    // ===== AUTO-TAGGING AND FOLLOW-UP INTEGRATION =====
    // Trigger AI analysis and follow-up scheduling after successful message processing
    try {
      // Get contact information for auto-analysis
      const { data: contact, error: contactError } = await supabase
        .from("contacts")
        .select("id, follow_up_enabled, total_messages")
        .eq("auth_id", customer.auth_id)
        .eq("phone_number", fromNumber)
        .single();

      if (contact && !contactError) {
        // Stop any existing follow-ups since customer replied
        if (contact.follow_up_enabled) {
          stopFollowUpsForContact(contact.id, 'customer_replied').catch(error => {
            logger.error("Error stopping follow-ups:", error);
          });
        }

        // Trigger AI analysis for follow-up scheduling (only for contacts with follow-ups enabled)
        if (contact.follow_up_enabled && contact.total_messages >= 2) {
          logger.info(`🔍 Starting AI analysis for follow-up | Contact: ${contact.id} | Messages: ${contact.total_messages}`);

          try {
            const analysis = await analyzeContactConversation(
              customer.auth_id,
              fromNumber,
              contact.id,
              'new_message'
            );

            if (analysis) {
              logger.info(`🤖 AI Analysis complete | Contact: ${contact.id} | Intent: ${analysis.intent_category} | Tags: ${analysis.auto_tags?.join(', ')}`);

              // Schedule follow-up if appropriate
              const followUpResult = await scheduleFollowUpFromAnalysis(
                contact.id,
                customer.auth_id,
                fromNumber,
                analysis
              );

              if (followUpResult.success) {
                logger.info(`📅 Follow-up scheduled | Contact: ${contact.id} | Intent: ${analysis.intent_category} | Delay: ${followUpResult.delayHours}h | Scheduled for: ${followUpResult.scheduledTime}`);
              } else {
                logger.info(`📅 Follow-up not scheduled | Contact: ${contact.id} | Intent: ${analysis.intent_category} | Reason: ${followUpResult.reason}`);
              }
            } else {
              logger.warn(`🤖 AI Analysis returned no results | Contact: ${contact.id}`);
            }
          } catch (analysisError) {
            logger.error("Error in AI analysis for follow-up:", analysisError);
          }
        } else {
          logger.info(`⏭️ Skipping AI analysis | Contact: ${contact.id} | Follow-up enabled: ${contact.follow_up_enabled} | Messages: ${contact.total_messages}`);
        }
      }
    } catch (autoTagError) {
      // Don't fail message processing if auto-tagging fails
      logger.error("Error in auto-tagging integration:", autoTagError);
    }

    const processingTime = Date.now() - processingStartTime;
    console.log(
      `✅ Message Processing Complete | From: ${fromNumber} | Duration: ${processingTime}ms | Time: ${new Date().toISOString()}`,
    );
  } catch (error) {
    const processingTime = Date.now() - processingStartTime;
    console.error(
      `❌ Message Processing Error | From: ${fromNumber} | Duration: ${processingTime}ms | Error:`,
      error,
    );
    // Send appropriate error message based on message type
    let errorMessage = "Sorry, I encountered an error processing your message. Please try again.";
    if (messageType === 'image') {
      errorMessage = "Sorry, I had trouble processing your image. Please try sending it again or describe what you wanted to show me.";
    } else if (messageType === 'audio') {
      errorMessage = "Sorry, I had trouble processing your voice message. Please try sending it again or send a text message instead.";
    }
    
    await sendWhatsAppMessage(fromNumber, errorMessage, phoneNumberId);
  }
}

// Helper function to handle regular chat
async function handleRegularChat(customer, fromNumber, message, phoneNumberId, rawTranscription = null) {
  try {
    // Use the existing AI chat functionality with knowledge base
    const response = await generateResponse(
      message,
      customer.auth_id,
      fromNumber,
      "gpt-4o",
      rawTranscription, // Pass raw transcription for better knowledge base search
    );

    // Check if response includes product images
    if (typeof response === 'object' && response.sendProductImages) {
      // Send text response first
      await sendMultipleWhatsAppMessages(fromNumber, response.text, phoneNumberId);

      // Then send product images
      try {
        const imagesSent = await sendProductImages(
          fromNumber,
          phoneNumberId,
          response.products,
          customer.system_access_token,
          3 // Max 3 images
        );

        if (imagesSent) {
          logger.debug(`Product images sent | From: ${fromNumber} | Count: ${response.products.length}`);
        }
      } catch (imageError) {
        console.error("Error sending product images:", imageError);
        // Continue without images - text response was already sent
      }
    } else {
      // Regular text response
      await sendMultipleWhatsAppMessages(fromNumber, response, phoneNumberId);
    }

    logger.debug(`Regular chat complete | From: ${fromNumber}`);
  } catch (error) {
    console.error("Error handling regular chat:", error);

    // Send a generic error message (OpenAI will detect and respond in appropriate language)
    const errorMessage = "Sorry, I encountered an error. Please try again.";
    await sendWhatsAppMessage(fromNumber, errorMessage, phoneNumberId);
  }
}

// Helper function to send WhatsApp text message
async function sendWhatsAppTextMessage(to, message, phoneNumberId, accessToken) {
  const sendStartTime = Date.now();
  try {
    console.log(
      `📤 Sending WhatsApp Text Message | To: ${to} | PhoneID: ${phoneNumberId} | Time: ${new Date().toISOString()}`,
    );

    const response = await axios.post(
      `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`,
      {
        messaging_product: "whatsapp",
        to: to,
        text: { body: message },
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      },
    );

    const sendTime = Date.now() - sendStartTime;
    console.log(
      `✅ WhatsApp Text Message Sent Successfully | To: ${to} | Duration: ${sendTime}ms | Time: ${new Date().toISOString()}`,
    );

    return response.data;
  } catch (error) {
    const sendTime = Date.now() - sendStartTime;
    console.error(
      `❌ WhatsApp Text Message Send Failed | To: ${to} | Duration: ${sendTime}ms | Error:`,
      error,
    );
    throw error;
  }
}

// Helper function to send WhatsApp image message
async function sendWhatsAppImageMessage(to, imageSource, phoneNumberId, accessToken, caption = '', customer = null) {
  const sendStartTime = Date.now();
  try {
    console.log(
      `📤 Sending WhatsApp Image Message | To: ${to} | PhoneID: ${phoneNumberId} | Time: ${new Date().toISOString()}`,
    );

    // Validate phone number format
    if (!to || typeof to !== 'string' || !/^\d{10,15}$/.test(to.replace(/\D/g, ''))) {
      throw new Error('Invalid phone number format. Must be 10-15 digits.');
    }

    let imagePayload;

    // Check if the source is a URL or a media ID
    if (imageSource.startsWith('http')) {
      // Validate image URL
      try {
        const imageCheck = await axios.get(imageSource, {
          responseType: 'arraybuffer',
          timeout: 10000, // Increased timeout to 10s
          maxContentLength: 16 * 1024 * 1024, // 16MB max
          validateStatus: function (status) {
            return status === 200; // Only accept 200
          },
          headers: {
            'Accept': 'image/*',
            'User-Agent': 'WhatsApp/2.24.1.84'
          }
        });

        // Validate content type
        const contentType = imageCheck.headers['content-type'];
        if (!contentType || !contentType.startsWith('image/')) {
          throw new Error(`Invalid content type: ${contentType}. Expected image/* but got ${contentType}`);
        }

        // Check if it's WebP format
        if (contentType === 'image/webp') {
          console.warn('⚠️ Warning: WebP format detected in URL. Consider using JPEG or PNG for better compatibility.');
        }

        // Validate file size
        const contentLength = parseInt(imageCheck.headers['content-length']);
        if (contentLength === 0) {
          throw new Error('Image file is empty');
        }
        if (contentLength < 1024) { // 1KB minimum
          throw new Error('Image file too small (minimum 1KB)');
        }
        if (contentLength > 16 * 1024 * 1024) { // 16MB limit
          throw new Error(`File size ${contentLength} bytes exceeds WhatsApp's 16MB limit`);
        }

        // Validate image dimensions if possible
        const imageBuffer = Buffer.from(imageCheck.data);
        try {
          const dimensions = await sharp(imageBuffer).metadata();
          if (dimensions.width < 192 || dimensions.height < 192) {
            console.warn('⚠️ Warning: Image dimensions are smaller than recommended minimum (192x192)');
          }
          if (dimensions.width > 5000 || dimensions.height > 5000) {
            console.warn('⚠️ Warning: Image dimensions are larger than recommended maximum (5000x5000)');
          }
        } catch (dimensionError) {
          console.warn('⚠️ Warning: Could not validate image dimensions:', dimensionError.message);
        }

        console.log(`✅ Image URL validation passed: ${imageSource}`);
        imagePayload = { link: imageSource };
      } catch (urlError) {
        console.error(`❌ Image URL validation failed:`, urlError);
        throw new Error(`Image URL validation failed: ${urlError.message}`);
      }
    } else {
      // Using media ID
      if (!imageSource || typeof imageSource !== 'string' || imageSource.trim().length === 0) {
        throw new Error('Invalid media ID provided');
      }

      // Verify media ID exists and is accessible
      try {
        await axios.get(
          `https://graph.facebook.com/v18.0/${imageSource}`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Accept': 'application/json',
            },
            timeout: 10000,
          }
        );
        console.log(`✅ Media ID verification passed: ${imageSource}`);
      } catch (verifyError) {
        throw new Error(`Invalid or inaccessible media ID: ${verifyError.message}`);
      }

      console.log(`📤 Using WhatsApp media ID: ${imageSource}`);
      imagePayload = { id: imageSource };
    }

    // Validate caption length (WhatsApp limit is 1000 characters)
    if (caption && caption.trim().length > 1000) {
      throw new Error('Caption exceeds WhatsApp limit of 1000 characters');
    }

    // Prepare image message payload
    const messagePayload = {
      messaging_product: "whatsapp",
      recipient_type: "individual",
      to: to.replace(/\D/g, ''), // Strip any non-digit characters
      type: "image",
      image: imagePayload,
    };

    // Add caption if provided
    if (caption && caption.trim()) {
      messagePayload.image.caption = caption.trim();
    }

    const response = await axios.post(
      `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`,
      messagePayload,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        timeout: 30000, // 30 second timeout
      },
    );

    if (!response.data || !response.data.messages || !response.data.messages[0]?.id) {
      throw new Error('Invalid response from WhatsApp API: Missing message ID');
    }

    const sendTime = Date.now() - sendStartTime;
    console.log(
      `✅ WhatsApp Image Message Sent Successfully | To: ${to} | Duration: ${sendTime}ms | Message ID: ${response.data.messages[0].id} | Time: ${new Date().toISOString()}`,
    );

    return response.data;
  } catch (error) {
    const sendTime = Date.now() - sendStartTime;
    console.error(
      `❌ WhatsApp Image Message Send Failed | To: ${to} | Duration: ${sendTime}ms | Error:`,
      error.response?.data || error.message,
    );
    throw new Error(`Failed to send image message: ${error.response?.data?.error?.message || error.message}`);
  }
}

// Helper function to save media message to chat history
async function saveMediaMessageToHistory(customer, phoneNumber, mediaType, mediaId) {
  try {
    // Save to chat history
    const { data: chatHistory, error: chatError } = await supabase
      .from("chat_history")
      .insert([
        {
          auth_id: customer.auth_id,
          phone_number: phoneNumber,
          role: 'assistant',
          content: `[Sent ${mediaType} message]`,
          created_at: new Date().toISOString(),
          media_type: mediaType,
          media_filename: `whatsapp-${mediaType}-${Date.now()}`,
          media_url: `https://graph.facebook.com/v18.0/${mediaId}`,
          mime_type: mediaType === 'audio' ? 'audio/mpeg' : 'audio/ogg'
        }
      ])
      .select()
      .single();

    if (chatError) {
      console.error("Error saving media message to chat history:", chatError);
      throw chatError;
    }

    console.log(`✅ Media message saved to chat history | Type: ${mediaType} | Phone: ${phoneNumber}`);
    return chatHistory;
  } catch (error) {
    console.error("Error in saveMediaMessageToHistory:", error);
    throw error;
  }
}



// Helper function to send WhatsApp video message
async function sendWhatsAppVideoMessage(to, videoSource, phoneNumberId, accessToken, caption = '') {
  const sendStartTime = Date.now();
  try {
    console.log(
      `📤 Sending WhatsApp Video Message | To: ${to} | PhoneID: ${phoneNumberId} | Time: ${new Date().toISOString()}`,
    );

    let videoPayload;

    // Check if the source is a URL or a media ID
    if (videoSource.startsWith('http')) {
      // Validate video URL
      try {
        const urlCheck = await axios.get(videoSource, {
          responseType: 'stream',
          timeout: 5000,
          maxContentLength: 16 * 1024 * 1024, // 16MB max
          validateStatus: function (status) {
            return status === 200; // Only accept 200
          },
          headers: {
            'Accept': 'video/*',
            'User-Agent': 'WhatsApp/2.24.1.84'
          }
        });

        // Validate content type
        const contentType = urlCheck.headers['content-type'];
        if (!contentType || !contentType.startsWith('video/')) {
          throw new Error(`Invalid content type: ${contentType}. Expected video/* but got ${contentType}`);
        }

        // Validate file size
        const contentLength = parseInt(urlCheck.headers['content-length']);
        if (contentLength > 16 * 1024 * 1024) { // 16MB limit
          throw new Error(`File size ${contentLength} bytes exceeds WhatsApp's 16MB limit`);
        }

        console.log(`✅ Video URL validation passed: ${videoSource}`);
        videoPayload = { link: videoSource };
      } catch (urlError) {
        console.error(`❌ Video URL validation failed:`, urlError);
        throw new Error(`Video URL validation failed: ${urlError.message}`);
      }
    } else {
      // Using media ID
      console.log(`📤 Using WhatsApp media ID: ${videoSource}`);
      videoPayload = { id: videoSource };
    }

    // Prepare video message payload
    const messagePayload = {
      messaging_product: "whatsapp",
      to: to,
      type: "video",
      video: videoPayload,
    };

    // Add caption if provided
    if (caption && caption.trim()) {
      messagePayload.video.caption = caption.trim();
    }

    const response = await axios.post(
      `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`,
      messagePayload,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      },
    );

    const sendTime = Date.now() - sendStartTime;
    console.log(
      `✅ WhatsApp Video Message Sent Successfully | To: ${to} | Duration: ${sendTime}ms | Time: ${new Date().toISOString()}`,
    );

    return response.data;
  } catch (error) {
    const sendTime = Date.now() - sendStartTime;
    console.error(
      `❌ WhatsApp Video Message Send Failed | To: ${to} | Duration: ${sendTime}ms | Error:`,
      error,
    );
    throw error;
  }
}

// Helper function to send multiple WhatsApp messages
async function sendMultipleWhatsAppMessages(to, message, phoneNumberId) {
  try {
    // Check if message contains multiple parts separated by |||
    if (message.includes('|||')) {
      const messageParts = message.split('|||').map(part => part.trim()).filter(part => part.length > 0);
      
      console.log(`📤 Sending ${messageParts.length} separate WhatsApp messages | To: ${to}`);
      
      const results = [];
      
      // Send each message part with a small delay
      for (let i = 0; i < messageParts.length; i++) {
        const part = messageParts[i];
        
        try {
          const result = await sendWhatsAppMessage(to, part, phoneNumberId);
          results.push(result);
          
          // Add small delay between messages (except for the last one)
          if (i < messageParts.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
          }
        } catch (error) {
          console.error(`❌ Failed to send message part ${i + 1}:`, error);
          // Continue with other parts even if one fails
          results.push({ error: error.message, part: part });
        }
      }
      
      console.log(`✅ Sent ${messageParts.length} messages successfully | To: ${to}`);
      return { multipleMessages: true, results: results, count: messageParts.length };
    } else {
      // Single message - use normal sending
      return await sendWhatsAppMessage(to, message, phoneNumberId);
    }
  } catch (error) {
    console.error("Error sending multiple WhatsApp messages:", error);
    throw error;
  }
}

// Helper function to send WhatsApp message (legacy function for backward compatibility)
async function sendWhatsAppMessage(to, message, phoneNumberId) {
  try {
    // Get customer's system access token
    const { data: customer, error: customerError } = await supabase
      .from("whatsapp_customers")
      .select("system_access_token")
      .eq("whatsapp_phone_number_id", phoneNumberId)
      .single();

    if (customerError || !customer?.system_access_token) {
      throw new Error(
        "Failed to get customer's system access token. Please check your WhatsApp configuration.",
      );
    }

    // Use the new text message function
    return await sendWhatsAppTextMessage(to, message, phoneNumberId, customer.system_access_token);
  } catch (error) {
    // Check if token is expired
    if (
      error.response?.status === 401 ||
      error.response?.data?.error?.code === 190
    ) {
      throw new Error(
        "WhatsApp access token has expired. Please update your system access token in the configuration.",
      );
    }

    throw error;
  }
}





// Analytics update function for message tracking
async function updateAnalytics(authId, phoneNumber, analytics) {
  try {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1;
    const currentDate = today.toISOString().split('T')[0];

    // Get current daily stats
    const { data: existingDailyStats, error: dailyError } = await supabase
      .from("daily_statistics")
      .select("*")
      .eq("auth_id", authId)
      .eq("date", currentDate)
      .single();

    // Initialize daily stats
    const dailyStats = {
      auth_id: authId,
      date: currentDate,
      total_messages: (existingDailyStats?.total_messages || 0) + 1,
      incoming_messages: (existingDailyStats?.incoming_messages || 0) + (analytics.messageType === 'incoming' ? 1 : 0),
      outgoing_messages: (existingDailyStats?.outgoing_messages || 0) + (analytics.messageType === 'outgoing' ? 1 : 0),
      unique_contacts: existingDailyStats?.unique_contacts || 0,
      total_tokens: (existingDailyStats?.total_tokens || 0) + (analytics.totalTokens || 0),
      prompt_tokens: (existingDailyStats?.prompt_tokens || 0) + (analytics.promptTokens || 0),
      completion_tokens: (existingDailyStats?.completion_tokens || 0) + (analytics.completionTokens || 0),
      avg_response_time_ms: analytics.messageType === 'outgoing' ? (analytics.responseTime || 0) : (existingDailyStats?.avg_response_time_ms || 0),
      knowledge_queries: (existingDailyStats?.knowledge_queries || 0) + (analytics.knowledgeQuery ? 1 : 0),
      knowledge_hits: (existingDailyStats?.knowledge_hits || 0) + (analytics.knowledgeHit ? 1 : 0)
    };

    // Update daily statistics
    await supabase
      .from("daily_statistics")
      .upsert(dailyStats, {
        onConflict: 'auth_id,date',
        ignoreDuplicates: false
      });

    // Update comprehensive monthly statistics using the database RPC function
    await supabase.rpc("update_monthly_statistics", {
      p_auth_id: authId,
      p_year: currentYear,
      p_month: currentMonth,
    });

    logger.debug(`Updated analytics | AuthId: ${authId} | Total: ${dailyStats.total_messages}`);
  } catch (error) {
    console.error("Error updating analytics:", error);
  }
}

// Update only contacts and knowledge queries in monthly usage (messages handled by increment_message_usage RPC)
async function updateMonthlyContactsAndKnowledge(authId, year, month) {
  try {
    // Get total unique contacts for this user
    const { count: totalContacts } = await supabase
      .from("contacts")
      .select("phone_number", { count: "exact", head: true })
      .eq("auth_id", authId);

    // Get new contacts for this month
    const startOfMonth = new Date(year, month - 1, 1).toISOString();
    const endOfMonth = new Date(year, month, 0, 23, 59, 59).toISOString();

    const { count: newContacts } = await supabase
      .from("contacts")
      .select("phone_number", { count: "exact", head: true })
      .eq("auth_id", authId)
      .gte("created_at", startOfMonth)
      .lte("created_at", endOfMonth);

    // Get knowledge base queries for this month
    const { count: knowledgeQueries } = await supabase
      .from("message_statistics")
      .select("id", { count: "exact", head: true })
      .eq("auth_id", authId)
      .eq("message_type", "outgoing")
      .gte("created_at", startOfMonth)
      .lte("created_at", endOfMonth);

    // Get current monthly usage record to preserve message counts
    const { data: currentUsage } = await supabase
      .from("monthly_usage")
      .select("messages_sent, messages_received")
      .eq("auth_id", authId)
      .eq("year", year)
      .eq("month", month)
      .single();

    // Update monthly usage preserving message counts from increment_message_usage RPC
    await supabase.from("monthly_usage").upsert(
      {
        auth_id: authId,
        year: year,
        month: month,
        total_contacts: totalContacts || 0,
        new_contacts: newContacts || 0,
        // Preserve existing message counts from increment_message_usage RPC
        messages_sent: currentUsage?.messages_sent || 0,
        messages_received: currentUsage?.messages_received || 0,
        knowledge_base_queries: knowledgeQueries || 0,
        updated_at: new Date().toISOString(),
      },
      {
        onConflict: "auth_id,year,month",
      },
    );

    console.log(`📊 Updated monthly contacts/knowledge | AuthId: ${authId} | Year: ${year} | Month: ${month} | Total Contacts: ${totalContacts} | New Contacts: ${newContacts} | Knowledge Queries: ${knowledgeQueries}`);
  } catch (error) {
    console.error("Error updating monthly contacts and knowledge:", error);
  }
}


async function generateResponse(prompt, authId, phoneNumber, model = "gpt-4o", rawTranscription = null) {
  const startTime = Date.now();

  try {
    // Step 1: Update/create contact information
    const contactResult = await supabase.rpc("upsert_contact", {
      p_auth_id: authId,
      p_phone_number: phoneNumber,
    });

    // Auto-analysis disabled - analysis logs removed

    // Track incoming message statistics and increment usage
    await Promise.all([
      supabase.from("message_statistics").insert([
        {
          auth_id: authId,
          phone_number: phoneNumber,
          message_type: "incoming",
          message_length: prompt.length,
          knowledge_sections_found: 0, // Will be updated with actual values later
          similarity_score: 0, // Will be updated with actual values later
        },
      ]),
      // Increment message usage count
      supabase.rpc("increment_message_usage", {
        p_auth_id: authId,
        p_message_type: "incoming",
      }),
      // Track incoming message analytics
      updateAnalytics(authId, phoneNumber, {
        messageType: 'incoming',
        knowledgeQuery: false,
        knowledgeHit: false,
        knowledgeSectionsFound: 0,
        similarityScore: 0,
        responseTime: 0,
        totalTokens: 0,
        promptTokens: 0,
        completionTokens: 0
      })
    ]);

    // Step 2: Get user's chatbot settings
    let { data: settings, error: settingsError } = await supabase
      .from("chatbot_settings")
      .select("*")
      .eq("auth_id", authId)
      .single();

    // If no settings found, create default settings
    if (settingsError && settingsError.code === "PGRST116") {
      const { data: newSettings, error: insertError } = await supabase
        .from("chatbot_settings")
        .insert([{ auth_id: authId }])
        .select()
        .single();

      if (insertError) throw new Error(insertError.message);
      settings = newSettings;
    } else if (settingsError) {
      throw new Error(settingsError.message);
    }
    // Use settings values or provided model parameter
    const chatModel = model !== "gpt-4o" ? model : settings.model;
    const chatHistoryLimit = settings.chat_history_limit;
    const systemPrompt = settings.system_prompt;
    const temperature = settings.temperature;
    const maxTokens = settings.max_tokens;
    const similarityThreshold = settings.similarity_threshold;
    const matchCount = settings.match_count;

    // Step 3: Get chat history (using user's preference)
    const { data: chatHistory, error: historyError } = await supabase
      .from("chat_history")
      .select("*")
      .eq("auth_id", authId)
      .eq("phone_number", phoneNumber)
      .order("created_at", { ascending: false })
      .limit(chatHistoryLimit);

    if (historyError) throw new Error(historyError.message);

    // Step 4: Convert the question into an embedding
    // Use raw transcription for knowledge base search if available (for voice messages)
    // This improves search accuracy by excluding metadata prefixes
    const searchText = rawTranscription || prompt;
    logger.debug(`Knowledge base search: "${searchText.substring(0, 50)}${searchText.length > 50 ? '...' : ''}"`);
    
    const embeddingResponse = await openai.embeddings.create({
      model: "text-embedding-3-small",
      input: searchText,
    });

    const questionEmbedding = embeddingResponse.data[0].embedding;

    // Step 5: Search the vector store for relevant knowledge using similarity search
    const { data: matchingSections, error } = await supabase.rpc(
      "match_knowledge_sections",
      {
        query_embedding: questionEmbedding,
        match_threshold: similarityThreshold,
        match_count: matchCount,
        p_auth_id: authId,
      },
    );

    if (error) throw new Error(error.message);

    // Step 5.5: Get product catalog for context
    const products = await getProductsWithImages(authId, false);
    const formattedProducts = formatProductsForAI(products);
    const productContext = products.length > 0
      ? `\n\nAVAILABLE PRODUCTS/SERVICES:\n${formattedProducts}`
      : '\n\nNo products/services available in catalog.';

    // Debug: Log product context when customer asks about specific products
    if (/avocado.*toast/i.test(prompt) || /can.*see.*how.*look/i.test(prompt)) {
      console.log(`🥑 Debug - Product context for visual inquiry: "${prompt}"`);
      console.log(`📦 Products found: ${products.length}`);
      console.log(`🖼️ Products with images: ${products.filter(p => p.image_url).length}`);
      console.log(`📝 Formatted products:`);
      console.log(formattedProducts);
      console.log(`🎯 Product context being sent to AI:`);
      console.log(productContext);
    }

    // Combine the relevant sections into a single context
    const knowledgeContext =
      matchingSections && matchingSections.length > 0
        ? matchingSections.map((section) => section.content).join("\n\n")
        : "No relevant information found in knowledge base.";

    // Step 5.6: Check if user is asking about order/booking tracking
    const isOrderTrackingQuery = /\b(track|status|order|booking|appointment|reservation|my.*order|order.*status|where.*order|check.*order|order.*update|delivery|shipped|confirm|confirmed)\b/i.test(prompt.toLowerCase()) ||
                                 /(订单|状态|跟踪|追踪|我的订单|订单状态|预约|预定|确认)/i.test(prompt) ||
                                 /\b(jejak|status|pesanan|tempahan|temujanji|order|saya.*order|order.*saya|hantar|pos|confirm)\b/i.test(prompt.toLowerCase());

    let orderTrackingContext = '';

    if (isOrderTrackingQuery) {
      try {
        // Get customer's WhatsApp configuration
        const { data: customer, error: customerError } = await supabase
          .from("whatsapp_customers")
          .select("*")
          .eq("auth_id", authId)
          .single();

        if (!customerError && customer && customer.spreadsheet_id) {
          // Get order status from Google Sheets
          const orderStatus = await getOrderStatusFromGoogleSheets(customer, phoneNumber);

          if (orderStatus.found && orderStatus.orders && orderStatus.orders.length > 0) {
            orderTrackingContext = `\n\nORDER TRACKING INFORMATION (PRIORITY - USE THIS FIRST):
Found ${orderStatus.count} order(s) for this customer:

${orderStatus.orders.map((order, index) => {
  const details = Object.entries(order.details)
    .filter(([key, value]) => value && value.toString().trim())
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n');
  return `Order ${index + 1} (Row ${order.rowIndex}):\n${details}`;
}).join('\n\n')}

INSTRUCTIONS: Provide this order information to the customer in a friendly, conversational way. Format it nicely and include relevant details like status, dates, products, etc. If multiple orders exist, show all of them clearly.`;
          } else {
            orderTrackingContext = `\n\nORDER TRACKING INFORMATION:
No orders found for this phone number in our records. The customer might be asking about order tracking, but we don't have any order information for them.

INSTRUCTIONS: Politely inform the customer that no orders were found for their phone number and suggest they contact the business directly or check if they used a different phone number.`;
          }
        }
      } catch (error) {
        console.error("Error checking order tracking:", error);
        orderTrackingContext = `\n\nORDER TRACKING INFORMATION:
Unable to check order status at the moment due to a technical issue.

INSTRUCTIONS: Apologize for the technical difficulty and suggest the customer contact the business directly for order status updates.`;
      }
    }

    const context = knowledgeContext + productContext + orderTrackingContext;

    // Get best similarity score for statistics
    const bestSimilarityScore =
      matchingSections && matchingSections.length > 0
        ? Math.max(...matchingSections.map((section) => section.similarity))
        : 0;

    // Track knowledge base query statistics
    const knowledgeQuery = true; // Every generateResponse involves a knowledge query
    const knowledgeHit = matchingSections && matchingSections.length > 0;

    // Step 6: Detect current message language
    const detectLanguage = (text) => {
      // Simple language detection based on character patterns
      const chinesePattern = /[\u4e00-\u9fff]/;
      const malayPattern = /\b(ada|apa|boleh|tak|tidak|saya|kami|dengan|untuk|dari|ini|itu|yang|dan|atau|juga|sudah|belum|akan|bisa|mau|nak|lah|kah|pun|je|la|ke|di|pada|dalam|atas|bawah|depan|belakang|kiri|kanan)\b/i;

      if (chinesePattern.test(text)) {
        return 'chinese';
      } else if (malayPattern.test(text)) {
        return 'malay';
      } else {
        return 'english';
      }
    };

    const currentLanguage = detectLanguage(prompt);
    console.log(`🌍 Detected language for "${prompt}": ${currentLanguage}`);

    // Step 7: Prepare conversation history
    const conversationHistory = chatHistory
      ? chatHistory.reverse().map((msg) => ({
          role: msg.role,
          content: msg.content,
        }))
      : [];

    // Step 8: Use OpenAI to generate a response with language priority
    const languageInstruction = {
      'english': 'IMPORTANT: The customer just wrote in ENGLISH. You MUST respond in ENGLISH, regardless of previous conversation language.',
      'chinese': 'IMPORTANT: The customer just wrote in CHINESE. You MUST respond in CHINESE, regardless of previous conversation language.',
      'malay': 'IMPORTANT: The customer just wrote in MALAY. You MUST respond in MALAY, regardless of previous conversation language.'
    };

    // Enhance user's simple prompt with technical implementation details
    const enhancedSystemPrompt = `${systemPrompt}

TECHNICAL IMPLEMENTATION (SYSTEM INSTRUCTIONS):
- LANGUAGE DETECTION: Match customer's language (English/Malay/Chinese/mix). Default: English. Use Malaysian style: "ya", "kan", "ah". Be conversational, not formal.
- KNOWLEDGE BASE: Only use provided knowledge base & product catalog. For services: always available. If unsure: "Sorry lah, not sure about that 😅"
- ORDER HANDLING: If order processing is disabled, redirect customers to contact business directly: "For orders, please contact us directly or visit our store ya! 😊"
- RESPONSE STYLE: Natural Malaysian expressions + relevant emojis. Give COMPLETE info directly - no follow-ups. DON'T ask "want more?" or "interested?". DON'T end with "feel free to ask". Just share info and stop.
- EXAMPLES: "Hi" → "Hi there! How can I help ya? 😊" | "你好" → "你好！有什么可以帮你的吗？😊"
- MULTIPLE MESSAGES: Use ||| ONLY for structured lists/promotions: "We got 3 promotions! 🎉|||First: Welcome offer RMXX off ✨|||Second: Buy 2 get 1 free 🎁|||Valid till month end ya!" For regular responses, write naturally - AI will auto-split long messages intelligently.

CURRENT CONTEXT (PRIORITY - USE THIS FIRST):
${context}

${languageInstruction[currentLanguage]}

MEMORY USAGE: If conversation history contains relevant information, use it only if the current context above doesn't already provide the answer. Always prioritize current context over memory.

IMAGES: Never include URLs/links. When customers ask to see products/photos:
- IMPORTANT: Look for "Image: Available" in the product information
- If ANY product shows "Image: Available", respond positively (e.g., "Let me show you!" or "Here's how it looks!") - the system will automatically send the images
- ONLY say "Sorry, no photos available 😅" if ALL products show "Image: Not available"
- Always check the AVAILABLE PRODUCTS section for image status before responding`;

    // Debug: Log system prompt when customer asks about visual content
    if (/can.*see.*how.*look/i.test(prompt)) {
      console.log(`🤖 Debug - System prompt for visual inquiry:`);
      console.log(`📝 Enhanced system prompt (last 500 chars):`);
      console.log(enhancedSystemPrompt.slice(-500));
    }

    const aiResponse = await openai.chat.completions.create({
      model: chatModel,
      messages: [
        {
          role: "system",
          content: enhancedSystemPrompt,
        },
        ...conversationHistory,
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: temperature,
      max_tokens: maxTokens,
    });

    const answer = aiResponse.choices[0].message.content;
    const responseTime = Date.now() - startTime;

    // Check if the user is asking for product recommendations or visual content
    const isProductInquiry = /\b(show|see|picture|image|photo|recommend|suggest|what.*have|available.*product|catalog|menu|options|look.*like|how.*look|appearance|visual|pic|img|pix|pics|images|photos|can.*see|how.*ur.*look|how.*your.*look)\b/i.test(prompt.toLowerCase()) ||
                            /(照片|图片|图像|相片|看看|显示|展示|推荐|建议|有什么|产品|菜单|样子|长什么样|外观|视觉|相|图|照|样)/i.test(prompt) ||
                            /\b(gambar|foto|tunjuk|tunjukkan|tengok|lihat|papar|cadang|cadangan|apa.*ada|produk|menu|rupa|macam.*mana|penampilan|visual|gbr|pic)\b/i.test(prompt.toLowerCase()) ||
                            /(有pic|有图|有照|看pic|看图|看照|show pic|show图|要pic|要图|要照|send pic|send图|send照|看一下|看下|给我看|可以.*看)/i.test(prompt.toLowerCase());
    const hasProductsWithImages = products.some(product => product.image_url);



    // If this seems like a product inquiry and we have products with images,
    // we'll send images after the text response
    let shouldSendImages = false;
    if (isProductInquiry && hasProductsWithImages) {
      console.log(`🔍 Product inquiry detected in regular chat mode: "${prompt}"`);
      // Use AI to determine if we should send product images
      try {
        const imageDecisionResponse = await openai.chat.completions.create({
          model: "gpt-4o-mini",
          messages: [
            {
              role: "system",
              content: `Determine if customer wants to see product images. Reply 'YES' if customer is:
- Asking to see/show/look at products ("can i see", "show me", "how does it look", "what does it look like")
- Asking for pictures/photos/images ("any pics", "got photo", "can see picture")
- Asking about appearance/visual ("how it looks", "what it looks like")
- Browsing products ("what do you have", "show me options")
Reply 'NO' for general questions, pricing only, or when not asking for visual content.`
            },
            {
              role: "user",
              content: `Customer message: "${prompt}"\nSend product images?`
            }
          ],
          max_tokens: 10,
          temperature: 0.1,
        });

        shouldSendImages = imageDecisionResponse.choices[0].message.content.trim().toUpperCase() === 'YES';
        console.log(`🤖 AI decision for sending images in regular chat: ${shouldSendImages ? 'YES' : 'NO'} (response: "${imageDecisionResponse.choices[0].message.content.trim()}")`);
      } catch (decisionError) {
        console.error("Error determining if images should be sent:", decisionError);
        // Fallback to basic keyword detection
        shouldSendImages = isProductInquiry;
      }
    }

    // Extract token usage from OpenAI response
    const usage = aiResponse.usage || {};
    const promptTokens = usage.prompt_tokens || 0;
    const completionTokens = usage.completion_tokens || 0;
    const totalTokens = usage.total_tokens || 0;

    // Step 8: Store the conversation in chat history with proper timing
    const userTimestamp = new Date().toISOString();
    
    // Store user message first
    await supabase.from("chat_history").insert({
      auth_id: authId,
      phone_number: phoneNumber,
      role: "user",
      content: prompt,
      created_at: userTimestamp,
    });
    
    // Store assistant message with slightly later timestamp
    const assistantTimestamp = new Date(Date.now() + 1000).toISOString(); // 1 second later
    await supabase.from("chat_history").insert({
      auth_id: authId,
      phone_number: phoneNumber,
      role: "assistant",
      content: answer,
      created_at: assistantTimestamp,
    });

    // Step 9: Track outgoing message statistics and increment usage
    await Promise.all([
      supabase.from("message_statistics").insert([
        {
          auth_id: authId,
          phone_number: phoneNumber,
          message_type: "outgoing",
          model_used: chatModel,
          prompt_tokens: promptTokens,
          completion_tokens: completionTokens,
          total_tokens: totalTokens,
          response_time_ms: responseTime,
          knowledge_sections_found: matchingSections
            ? matchingSections.length
            : 0,
          similarity_score: bestSimilarityScore,
          message_length: answer.length,
        },
      ]),
      // Increment outgoing message usage count
      supabase.rpc("increment_message_usage", {
        p_auth_id: authId,
        p_message_type: "outgoing",
      }),
    ]);

    // Step 10: Update comprehensive analytics
    await updateAnalytics(authId, phoneNumber, {
      messageType: 'outgoing',
      knowledgeQuery,
      knowledgeHit,
      knowledgeSectionsFound: matchingSections ? matchingSections.length : 0,
      similarityScore: bestSimilarityScore,
      responseTime,
      totalTokens,
      promptTokens,
      completionTokens
    });

    // Step 11: Automatically send images from matched knowledge base sections (with AI decision)
    await sendKnowledgeBaseImages(authId, phoneNumber, matchingSections, prompt);

    // Step 12: Send product images if appropriate
    if (shouldSendImages) {
      try {
        // Get all products with images
        const allProductsWithImages = await getProductsWithImages(authId, true);
        if (allProductsWithImages.length > 0) {
          // Use AI to determine which specific products are relevant to the customer's query
          const relevantProductsResponse = await openai.chat.completions.create({
            model: "gpt-4o-mini",
            messages: [
              {
                role: "system",
                content: `Return relevant product names as JSON array only. No extra text.
Examples:
"blueberry scone" → ["Blueberry Scone"]
"avocado toast" → ["Avocado Toast"]
"how ur avocado toast look" → ["Avocado Toast"]
"what desserts" → ["Blueberry Scone", "Chocolate Cake"]
"show me food" → ["Avocado Toast", "Blueberry Scone"]
Match products by name, partial name, or category. Be flexible with spelling and casual language.`
              },
              {
                role: "user",
                content: `Message: "${prompt}"\nProducts: ${JSON.stringify(allProductsWithImages.map(p => p.name))}`
              }
            ],
            max_tokens: 200,
            temperature: 0.1,
          });

          let relevantProductNames = [];
          try {
            // Clean the response by removing markdown code blocks and extra formatting
            let responseContent = relevantProductsResponse.choices[0].message.content.trim();

            // Remove markdown code blocks (```json ... ``` or ``` ... ```)
            responseContent = responseContent.replace(/```(?:json)?\s*([\s\S]*?)\s*```/g, '$1');

            // Remove any leading/trailing whitespace again
            responseContent = responseContent.trim();

            // Try to parse as JSON
            relevantProductNames = JSON.parse(responseContent);

            // Ensure it's an array
            if (!Array.isArray(relevantProductNames)) {
              console.error("Relevant products response is not an array:", relevantProductNames);
              relevantProductNames = [];
            }
          } catch (parseError) {
            console.error("Error parsing relevant products response:", parseError);
            console.error("Raw response:", relevantProductsResponse.choices[0].message.content);
            // Fallback: if parsing fails, don't send any images to avoid sending irrelevant ones
            relevantProductNames = [];
          }

          // Filter products to only include relevant ones
          const relevantProducts = allProductsWithImages.filter(product =>
            relevantProductNames.includes(product.name)
          );
          console.log(`🎯 Relevant products found in regular chat: ${relevantProducts.map(p => p.name).join(', ')} (from AI response: ${relevantProductNames.join(', ')})`);

          if (relevantProducts.length > 0) {
            // We'll return a special response that includes image sending instructions
            // The WhatsApp handler will need to process this
            return {
              text: answer,
              sendProductImages: true,
              products: relevantProducts.slice(0, 3), // Limit to 3 images
              phoneNumber: phoneNumber
            };
          } else if (relevantProductNames.length > 0) {
            // Customer asked for specific products but none have images
            // Check if the mentioned products exist (even without images)
            const allProducts = await getProductsWithImages(authId, false); // Get all products
            const mentionedProductsExist = allProducts.filter(product =>
              relevantProductNames.includes(product.name)
            );

            if (mentionedProductsExist.length > 0) {
              // Products exist but don't have images
              const productNames = mentionedProductsExist.map(p => p.name).join(", ");
              const apologyMessage = mentionedProductsExist.length === 1
                ? `Sorry, I don't have a photo of ${productNames} available right now 😅 But I can tell you more about it if you'd like!`
                : `Sorry, I don't have photos of ${productNames} available right now 😅 But I can tell you more about them if you'd like!`;

              return `${answer}\n\n${apologyMessage}`;
            }
          }
        }
      } catch (imageError) {
        console.error("Error preparing product images:", imageError);
        // Continue with text-only response
      }
    }

    return answer;
  } catch (error) {
    console.error("Error in generateResponse:", error);

    // Track failed response with analytics
    const responseTime = Date.now() - startTime;

    await Promise.all([
      supabase.from("message_statistics").insert([
        {
          auth_id: authId,
          phone_number: phoneNumber,
          message_type: "outgoing",
          model_used: model,
          response_time_ms: responseTime,
          knowledge_sections_found: 0,
          similarity_score: 0,
          message_length: 0,
        },
      ]),
      // Update analytics for failed response
      updateAnalytics(authId, phoneNumber, {
        messageType: 'outgoing',
        knowledgeQuery: true,
        knowledgeHit: false,
        knowledgeSectionsFound: 0,
        similarityScore: 0,
        responseTime,
        totalTokens: 0,
        promptTokens: 0,
        completionTokens: 0
      }),
    ]).catch((err) => console.error("Error tracking failed response:", err));

    throw error;
  }
}

// ===== ORDER PROCESSING ROUTES =====

// Get orders for a user
router.get("/orders", async (req, res) => {
  try {
    const {
      authId,
      phoneNumber,
      status,
      sortBy = "created_at",
      sortOrder = "desc",
    } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    // Get pagination parameters
    const { limit, page, offset } = getPaginationParams(req.query);

    // Build query with filters
    let query = supabase
      .from("orders")
      .select("*", { count: "exact" })
      .eq("auth_id", authId);

    // Apply filters
    if (phoneNumber) {
      query = query.eq("phone_number", phoneNumber);
    }

    if (status) {
      query = query.eq("order_status", status);
    }

    // Apply sorting
    const validSortFields = ["created_at", "updated_at", "order_status"];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "created_at";
    const ascending = sortOrder === "asc";

    query = query.order(sortField, { ascending });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) throw error;

    res.json(buildPaginatedResponse(data, count || 0, page, limit));
  } catch (error) {
    console.error("Error getting orders:", error);
    res.status(500).json({ error: "Failed to get orders" });
  }
});

// Update order status
router.put("/orders/:orderId", async (req, res) => {
  try {
    const { orderId } = req.params;
    const { authId, orderStatus, customerInfo, orderSummary } = req.body;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required field: authId",
      });
    }

    const updateData = {};
    if (orderStatus !== undefined) updateData.order_status = orderStatus;
    if (customerInfo !== undefined) updateData.customer_info = customerInfo;
    if (orderSummary !== undefined) updateData.order_summary = orderSummary;

    const { data, error } = await supabase
      .from("orders")
      .update(updateData)
      .eq("id", orderId)
      .eq("auth_id", authId)
      .select()
      .single();

    if (error) throw error;

    if (!data) {
      return res.status(404).json({
        error: "Order not found or you do not have permission to update it",
      });
    }

    // Update monthly statistics if order status changed
    if (orderStatus) {
      const now = new Date();
      await supabase.rpc("update_monthly_statistics", {
        p_auth_id: authId,
        p_year: now.getFullYear(),
        p_month: now.getMonth() + 1,
      });
    }

    res.json({
      success: true,
      message: "Order updated successfully",
      data: data,
    });
  } catch (error) {
    console.error("Error updating order:", error);
    res.status(500).json({ error: "Failed to update order" });
  }
});

// Delete order
router.delete("/orders/:orderId", async (req, res) => {
  try {
    const { orderId } = req.params;
    const { authId } = req.body;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required field: authId",
      });
    }

    const { data, error } = await supabase
      .from("orders")
      .delete()
      .eq("id", orderId)
      .eq("auth_id", authId)
      .select("id, order_status")
      .single();

    if (error) throw error;

    if (!data) {
      return res.status(404).json({
        error: "Order not found or you do not have permission to delete it",
      });
    }

    res.json({
      success: true,
      message: "Order deleted successfully",
      data: data,
    });
  } catch (error) {
    console.error("Error deleting order:", error);
    res.status(500).json({ error: "Failed to delete order" });
  }
});

// Toggle order processing feature
router.put("/settings/order-processing", async (req, res) => {
  try {
    const { authId, enabled } = req.body;

    if (!authId || enabled === undefined) {
      return res.status(400).json({
        error: "Missing required fields: authId, enabled",
      });
    }

    // Check if user has a paid plan
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("plan")
      .eq("auth_id", authId)
      .single();

    if (profileError) {
      return res.status(500).json({ error: "Failed to check user profile" });
    }

    if (!profile || ['free', 'trial'].includes(profile.plan)) {
      return res.status(403).json({
        error: "Order processing is only available for paid plans. Please upgrade your subscription.",
      });
    }

    // Update or create chatbot settings
    const { data, error } = await supabase
      .from("chatbot_settings")
      .upsert({
        auth_id: authId,
        order_processing_enabled: enabled,
      })
      .select()
      .single();

    if (error) throw error;

    res.json({
      success: true,
      message: `Order processing ${enabled ? 'enabled' : 'disabled'} successfully`,
      data: {
        order_processing_enabled: enabled,
        plan: profile.plan,
      },
    });
  } catch (error) {
    console.error("Error toggling order processing:", error);
    res.status(500).json({ error: "Failed to update order processing settings" });
  }
});

// Get products for order suggestions (for admin to see what customers see)
router.get("/products/for-orders", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    // Get active products for this business
    const { data: products, error } = await supabase
      .from("product_catalog")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .order("category", { ascending: true })
      .order("name", { ascending: true });

    if (error) throw error;

    // Format products as the AI would see them
    const formattedProducts = formatProductsForAI(products);

    res.json({
      success: true,
      data: {
        products: products,
        count: products.length,
        formatted_for_ai: formattedProducts,
        categories: [...new Set(products.map(p => p.category).filter(Boolean))],
        out_of_stock: products.filter(p => p.stock_quantity === 0),
      },
    });
  } catch (error) {
    console.error("Error getting products for orders:", error);
    res.status(500).json({ error: "Failed to get products" });
  }
});

// Update product stock (useful for order management)
router.put("/products/:productId/stock", async (req, res) => {
  try {
    const { productId } = req.params;
    const { authId, stockQuantity } = req.body;

    if (!authId || stockQuantity === undefined) {
      return res.status(400).json({
        error: "Missing required fields: authId, stockQuantity",
      });
    }

    const { data, error } = await supabase
      .from("product_catalog")
      .update({ stock_quantity: stockQuantity })
      .eq("id", productId)
      .eq("auth_id", authId)
      .select()
      .single();

    if (error) throw error;

    if (!data) {
      return res.status(404).json({
        error: "Product not found or you do not have permission to update it",
      });
    }

    res.json({
      success: true,
      message: "Product stock updated successfully",
      data: data,
    });
  } catch (error) {
    console.error("Error updating product stock:", error);
    res.status(500).json({ error: "Failed to update product stock" });
  }
});

// Chat endpoint for frontend compatibility
router.post("/chat", async (req, res) => {
  try {
    const { prompt, authId, phoneNumber } = req.body;

    if (!prompt || !authId || !phoneNumber) {
      return res.status(400).json({
        error: "Missing required fields: prompt, authId, phoneNumber",
      });
    }

    // Check user quotas before sending
    const { data: quotaCheck, error: quotaError } = await supabase.rpc(
      "check_user_quotas",
      { p_auth_id: authId },
    );

    if (quotaError) {
      return res.status(500).json({ error: "Failed to check user quota" });
    }

    const quotaInfo = quotaCheck[0];

    if (!quotaInfo || !quotaInfo.subscription_active) {
      return res.status(403).json({
        error: quotaInfo?.subscription_status === "expired"
          ? "Your subscription has expired. Please renew to continue using the chatbot."
          : "No active subscription found. Please subscribe to use the chatbot.",
      });
    }

    if (!quotaInfo.can_send_message) {
      return res.status(429).json({
        error: `You have reached your monthly message limit (${quotaInfo.current_message_count}/${quotaInfo.message_limit}). Please upgrade your plan or wait for next month.`,
      });
    }

    // Get customer WhatsApp configuration
    const { data: customer, error: customerError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .single();

    if (customerError || !customer) {
      return res.status(404).json({
        error: "WhatsApp configuration not found. Please set up your WhatsApp integration first.",
      });
    }

    // Send text message
    const messageResult = await sendWhatsAppTextMessage(
      phoneNumber,
      prompt,
      customer.whatsapp_phone_number_id,
      customer.system_access_token
    );

    // Update contact information
    await supabase.rpc("upsert_contact", {
      p_auth_id: authId,
      p_phone_number: phoneNumber,
    });

    // Track message statistics
    await Promise.all([
      supabase.from("message_statistics").insert([
        {
          auth_id: authId,
          phone_number: phoneNumber,
          message_type: "outgoing",
          message_length: prompt.length,
          model_used: 'manual_send',
        },
      ]),
      // Increment outgoing message usage count
      supabase.rpc("increment_message_usage", {
        p_auth_id: authId,
        p_message_type: "outgoing",
      }),
    ]);

    // Store in chat history
    await supabase.from("chat_history").insert([
      {
        auth_id: authId,
        phone_number: phoneNumber,
        role: "assistant",
        content: prompt,
      },
    ]);

    res.json({
      success: true,
      message: "Message sent successfully",
      data: messageResult,
    });
  } catch (error) {
    console.error("Error in chat endpoint:", error);
    res.status(500).json({ error: "Failed to send message" });
  }
});

// Send messages to WhatsApp (text, image, or video)
router.post("/send-message", async (req, res) => {
  try {
    const { 
      authId, 
      phoneNumber, 
      messageType = 'text', 
      content, 
      imageUrl, 
      videoUrl,
      caption 
    } = req.body;

    // Validate required fields
    if (!authId || !phoneNumber) {
      return res.status(400).json({
        error: "Missing required fields: authId, phoneNumber",
      });
    }

    // Validate message content based on type
    if (messageType === 'text' && !content) {
      return res.status(400).json({
        error: "Content is required for text messages",
      });
    }

    // For media messages, check for either URL or media ID
    if (messageType === 'image' && !(imageUrl || req.body.mediaId)) {
      return res.status(400).json({
        error: "Either imageUrl or mediaId is required for image messages",
      });
    }

    if (messageType === 'video' && !(videoUrl || req.body.mediaId)) {
      return res.status(400).json({
        error: "Either videoUrl or mediaId is required for video messages",
      });
    }

    // Check user quotas before sending
    const { data: quotaCheck, error: quotaError } = await supabase.rpc(
      "check_user_quotas",
      { p_auth_id: authId },
    );

    if (quotaError) {
      return res.status(500).json({ error: "Failed to check user quota" });
    }

    const quotaInfo = quotaCheck[0];

    if (!quotaInfo || !quotaInfo.subscription_active) {
      return res.status(403).json({
        error: quotaInfo?.subscription_status === "expired"
          ? "Your subscription has expired. Please renew to continue using the chatbot."
          : "No active subscription found. Please subscribe to use the chatbot.",
      });
    }

    if (!quotaInfo.can_send_message) {
      return res.status(429).json({
        error: `You have reached your monthly message limit (${quotaInfo.current_message_count}/${quotaInfo.message_limit}). Please upgrade your plan or wait for next month.`,
      });
    }

    // Get customer WhatsApp configuration
    const { data: customer, error: customerError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .single();

    if (customerError || !customer) {
      return res.status(404).json({
        error: "WhatsApp configuration not found. Please set up your WhatsApp integration first.",
      });
    }

    let messageResult;
    let sentMessageInfo = {};

    // Send message based on type
    switch (messageType) {
      case 'text':
        messageResult = await sendWhatsAppTextMessage(
          phoneNumber,
          content,
          customer.whatsapp_phone_number_id,
          customer.system_access_token
        );
        sentMessageInfo = {
          type: 'text',
          content: content,
          length: content.length,
        };
        break;

      case 'image':
        messageResult = await sendWhatsAppImageMessage(
          phoneNumber,
          req.body.mediaId || imageUrl, // Use mediaId if available, otherwise use URL
          customer.whatsapp_phone_number_id,
          customer.system_access_token,
          caption,
          customer
        );
        sentMessageInfo = {
          type: 'image',
          ...(req.body.mediaId ? { mediaId: req.body.mediaId } : { imageUrl: imageUrl }),
          caption: caption || '',
        };
        break;



      case 'video':
        messageResult = await sendWhatsAppVideoMessage(
          phoneNumber,
          req.body.mediaId || videoUrl, // Use mediaId if available, otherwise use URL
          customer.whatsapp_phone_number_id,
          customer.system_access_token,
          caption,
          customer
        );
        sentMessageInfo = {
          type: 'video',
          ...(req.body.mediaId ? { mediaId: req.body.mediaId } : { videoUrl: videoUrl }),
          caption: caption || '',
        };
        break;

      default:
        return res.status(400).json({
          error: "Invalid messageType. Supported types: text, image, video",
        });
    }

    // Update contact information
    await supabase.rpc("upsert_contact", {
      p_auth_id: authId,
      p_phone_number: phoneNumber,
    });

    // Track message statistics
    await Promise.all([
      supabase.from("message_statistics").insert([
        {
          auth_id: authId,
          phone_number: phoneNumber,
          message_type: "outgoing",
          message_length: sentMessageInfo.content?.length || 0,
          model_used: 'manual_send', // Distinguish from AI-generated messages
        },
      ]),
      // Increment outgoing message usage count
      supabase.rpc("increment_message_usage", {
        p_auth_id: authId,
        p_message_type: "outgoing",
      }),
    ]);

    // Store in chat history for context
    let chatContent = '';
    if (messageType === 'text') {
      chatContent = content;
    } else if (messageType === 'image') {
      chatContent = `[Sent an image${caption ? `: ${caption}` : ''}]`;
    } else if (messageType === 'video') {
      chatContent = `[Sent a video${caption ? `: ${caption}` : ''}]`;
    }

    await supabase.from("chat_history").insert([
      {
        auth_id: authId,
        phone_number: phoneNumber,
        role: "assistant",
        content: chatContent,
      },
    ]);

    // Update analytics
    const now = new Date();
    await supabase.rpc("update_monthly_statistics", {
      p_auth_id: authId,
      p_year: now.getFullYear(),
      p_month: now.getMonth() + 1,
    });

    // Update human takeover session activity if there's an active session
    try {
      await supabase.rpc("update_human_takeover_activity", {
        p_auth_id: authId,
        p_phone_number: phoneNumber,
      });
    } catch (humanTakeoverError) {
      // Don't fail the message sending if human takeover update fails
      console.error("Error updating human takeover activity:", humanTakeoverError);
    }

    res.json({
      success: true,
      message: "Message sent successfully",
      data: {
        whatsappMessageId: messageResult.messages?.[0]?.id,
        phoneNumber: phoneNumber,
        messageType: messageType,
        sentAt: new Date().toISOString(),
        ...sentMessageInfo,
      },
    });

  } catch (error) {
    console.error("Error sending WhatsApp message:", error);
    res.status(500).json({ 
      error: "Failed to send message",
      details: error.message,
    });
  }
});

// Upload media files (images, audio, video)
router.post("/upload-media", upload.single('media'), async (req, res) => {
  try {
    const { authId, phoneNumber } = req.body;
    
    logger.debug(`Starting media upload | AuthID: ${authId}, Phone: ${phoneNumber}, File: ${req.file?.originalname || 'NO FILE'}`);



    if (!phoneNumber) {
      return res.status(400).json({
        error: "Missing required field: phoneNumber",
      });
    }

    if (!authId) {
      return res.status(400).json({
        error: "Missing required field: authId",
      });
    }

    if (!req.file) {
      return res.status(400).json({
        error: "No file uploaded. Please select a media file.",
      });
    }

    // Get customer WhatsApp configuration
    logger.debug(`Looking up customer config for authId: ${authId}`);
    const { data: customer, error: customerError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .single();

    if (customerError || !customer) {
      logger.error("Customer config error:", customerError);
      return res.status(404).json({
        error: "WhatsApp configuration not found",
      });
    }
    
    logger.debug(`Customer found: ${customer.whatsapp_phone_number_id}`);

    // Check user quotas
    const { data: quotaCheck, error: quotaError } = await supabase.rpc(
      "check_user_quotas",
      { p_auth_id: authId },
    );

    if (quotaError) {
      console.error("Error checking quotas during upload:", quotaError);
    }

    const quotaInfo = quotaCheck?.[0];
    if (quotaInfo && (!quotaInfo.subscription_active)) {
      return res.status(403).json({
        error: quotaInfo?.subscription_status === "expired"
          ? "Your subscription has expired. Please renew to continue using the chatbot."
          : "No active subscription found. Please subscribe to use the chatbot.",
      });
    }

    // Map MIME type if needed
    const mappedMimeType = mimeTypeMapping[req.file.mimetype] || req.file.mimetype;
    logger.debug(`MIME Type Mapping | Original: ${req.file.mimetype} | Mapped: ${mappedMimeType}`);

    // Upload to WhatsApp's servers first
    const whatsappMediaId = await uploadMediaToWhatsApp(
      req.file.buffer,
      mappedMimeType,
      customer.whatsapp_phone_number_id,
      customer.system_access_token
    );
    logger.info(`WhatsApp upload complete | Media ID: ${whatsappMediaId}`);

    // Send the message using the WhatsApp media ID
    let messageResult;
    const messageType = req.file.mimetype.startsWith('image/') ? 'image' :
                       req.file.mimetype.startsWith('video/') ? 'video' : null;

    if (!messageType) {
      return res.status(400).json({
        error: "Unsupported media type. Only image and video are supported."
      });
    }

    const { caption } = req.body;
    
    logger.info(`Sending ${messageType} message to ${phoneNumber} with media ID: ${whatsappMediaId}`);

    switch (messageType) {
      case 'image':
        messageResult = await sendWhatsAppImageMessage(
          phoneNumber,
          whatsappMediaId,
          customer.whatsapp_phone_number_id,
          customer.system_access_token,
          caption
        );
        break;
      case 'video':
        messageResult = await sendWhatsAppVideoMessage(
          phoneNumber,
          whatsappMediaId,
          customer.whatsapp_phone_number_id,
          customer.system_access_token,
          caption
        );
        break;
    }
    
    logger.debug(`Message send result:`, messageResult);

    // Generate unique filename
    const timestamp = Date.now();
    const random = Math.round(Math.random() * 1E9);
    const ext = path.extname(req.file.originalname);
    const filename = `media-${timestamp}-${random}${ext}`;

    // Upload to Supabase Storage (for backup/history)
    const { data: uploadData, error: uploadError } = await supabase
      .storage
      .from('whatsapp-media')
      .upload(`${authId}/${filename}`, req.file.buffer, {
        contentType: req.file.mimetype,
        cacheControl: '31536000',
        upsert: false
      });

    if (uploadError) {
      console.error("Error uploading to Supabase Storage:", uploadError);
      return res.status(500).json({ error: "Failed to upload file" });
    }

    // Get public URL
    const { data: { publicUrl } } = supabase
      .storage
      .from('whatsapp-media')
      .getPublicUrl(`${authId}/${filename}`);

    // Store file info in database
    const { data: savedMedia, error: dbError } = await supabase
      .from("uploaded_media")
      .insert([
        {
          auth_id: authId,
          filename: filename,
          original_name: req.file.originalname,
          media_type: req.file.mimetype.split('/')[0],
          mime_type: req.file.mimetype,
          file_size: req.file.size,
          file_url: publicUrl,
          storage_path: `${authId}/${filename}`,
          uploaded_at: new Date().toISOString(),
          source: 'manual_upload'
          // whatsapp_media_id will be added after database migration
        },
      ])
      .select()
      .single();

    if (dbError) {
      console.error("Error saving to database:", dbError);
      return res.status(500).json({ error: "Failed to save file information" });
    }

    // --- NEW: Insert chat message with media info into chat_history ---
    const chatMessageContent =
      messageType === 'image'
        ? '[You sent an image]'
        : messageType === 'video'
        ? '[You sent a video]'
        : '[You sent a media]';
    const { data: chatMessage, error: chatError } = await supabase
      .from('chat_history')
      .insert([
        {
          auth_id: authId,
          phone_number: phoneNumber,
          role: 'assistant',
          content: caption ? `${chatMessageContent} ${caption}` : chatMessageContent,
          media_filename: filename,
          media_url: publicUrl,
          media_type: messageType,
          mime_type: req.file.mimetype,
          file_size: req.file.size,
          created_at: new Date().toISOString(),
        },
      ])
      .select()
      .single();
    if (chatError) {
      console.error('Error inserting chat message with media:', chatError);
      // Don't fail the upload, but log the error
    }
    // --- END NEW ---

    res.json({
      success: true,
      message: "File uploaded and message sent successfully",
      data: {
        upload: {
          filename: filename,
          originalName: req.file.originalname,
          mediaType: req.file.mimetype.split('/')[0],
          mimeType: req.file.mimetype,
          fileSize: req.file.size,
          fileUrl: publicUrl,
          uploadedAt: savedMedia.uploaded_at,
          whatsappMediaId: whatsappMediaId
        },
        message: messageResult,
        chatMessage: chatMessage || null
      },
    });

  } catch (error) {
    logger.error("Error uploading media:", error);
    res.status(500).json({ 
      error: "Failed to upload media file",
      details: error.message 
    });
  }
});

// Serve media files with proper headers and streaming support
router.get("/media/:filename", async (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(process.cwd(), 'uploads', filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: "File not found" });
    }

    const stat = fs.statSync(filePath);
    const fileSize = stat.size;
    const ext = path.extname(filename).toLowerCase();

    // Map file extensions to MIME types
    const mimeTypes = {
      '.mp3': 'audio/mpeg',
      '.ogg': 'audio/ogg',
      '.wav': 'audio/wav',
      '.m4a': 'audio/mp4',
      '.aac': 'audio/aac',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.mp4': 'video/mp4',
      '.mpeg': 'video/mpeg',
      '.webm': 'video/webm'
    };

    const contentType = mimeTypes[ext] || 'application/octet-stream';

    // Handle range requests for audio/video streaming
    const range = req.headers.range;
    if (range && (contentType.startsWith('audio/') || contentType.startsWith('video/'))) {
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunksize = (end - start) + 1;
      const file = fs.createReadStream(filePath, { start, end });

      const head = {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Length': chunksize,
        'Content-Type': contentType,
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'public, max-age=31536000',
      };

      res.writeHead(206, head);
      file.pipe(res);
    } else {
      // For non-range requests
      const head = {
        'Content-Length': fileSize,
        'Content-Type': contentType,
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'public, max-age=31536000',
        'Accept-Ranges': 'bytes',
      };

      res.writeHead(200, head);
      fs.createReadStream(filePath).pipe(res);
    }
  } catch (error) {
    console.error("Error serving media file:", error);
    res.status(500).json({ error: "Failed to serve media file" });
  }
});

// Enhanced send message endpoint that accepts both files and URLs
router.post("/send-message-with-file", upload.single('media'), async (req, res) => {
  try {
    let { 
      authId, 
      phoneNumber, 
      messageType = 'text', 
      content, 
      imageUrl, 
      videoUrl,
      caption 
    } = req.body;

    // Validate required fields
    if (!authId || !phoneNumber) {
      return res.status(400).json({
        error: "Missing required fields: authId, phoneNumber",
      });
    }

    let mediaUrl = null;
    let whatsappMediaId = null;
    
    // If file was uploaded, use the uploaded file
    if (req.file) {
      // Auto-detect message type based on uploaded file
      if (req.file.mimetype.startsWith('image/')) {
        messageType = 'image';
      } else if (req.file.mimetype.startsWith('video/')) {
        messageType = 'video';
      }
    } else {
      // Use provided URLs
      switch (messageType) {
        case 'image':
          mediaUrl = imageUrl;
          break;
        case 'video':
          mediaUrl = videoUrl;
          break;
      }
    }

    // Validate message content based on type
    if (messageType === 'text' && !content) {
      return res.status(400).json({
        error: "Content is required for text messages",
      });
    }

    // Validate media requirements
    if (['image', 'video'].includes(messageType)) {
      if (!req.file && !mediaUrl) {
        return res.status(400).json({
          error: `${messageType} message requires either a file upload or a valid ${messageType} URL`,
        });
      }
    }

    // Check user quotas before sending
    const { data: quotaCheck, error: quotaError } = await supabase.rpc(
      "check_user_quotas",
      { p_auth_id: authId },
    );

    if (quotaError) {
      return res.status(500).json({ error: "Failed to check user quota" });
    }

    const quotaInfo = quotaCheck[0];

    if (!quotaInfo || !quotaInfo.subscription_active) {
      return res.status(403).json({
        error: quotaInfo?.subscription_status === "expired"
          ? "Your subscription has expired. Please renew to continue using the chatbot."
          : "No active subscription found. Please subscribe to use the chatbot.",
      });
    }

    if (!quotaInfo.can_send_message) {
      return res.status(429).json({
        error: `You have reached your monthly message limit (${quotaInfo.current_message_count}/${quotaInfo.message_limit}). Please upgrade your plan or wait for next month.`,
      });
    }

    // Get customer WhatsApp configuration
    const { data: customer, error: customerError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .single();

    if (customerError || !customer) {
      return res.status(404).json({
        error: "WhatsApp configuration not found. Please set up your WhatsApp integration first.",
      });
    }

    // Upload file to WhatsApp if a file was uploaded
    if (req.file && ['image', 'video'].includes(messageType)) {
      // Map MIME type if needed
      const mappedMimeType = mimeTypeMapping[req.file.mimetype] || req.file.mimetype;

      // Upload to WhatsApp's servers
      whatsappMediaId = await uploadMediaToWhatsApp(
        req.file.buffer,
        mappedMimeType,
        customer.whatsapp_phone_number_id,
        customer.system_access_token
      );

      console.log(`✅ File uploaded to WhatsApp with Media ID: ${whatsappMediaId}`);
    }

    let messageResult;
    let sentMessageInfo = {};

    // Send message based on type
    console.log(`📤 Sending ${messageType} message to ${phoneNumber} | MediaID: ${whatsappMediaId || 'N/A'} | MediaURL: ${mediaUrl || 'N/A'}`);
    
    switch (messageType) {
      case 'text':
        messageResult = await sendWhatsAppTextMessage(
          phoneNumber,
          content,
          customer.whatsapp_phone_number_id,
          customer.system_access_token
        );
        sentMessageInfo = {
          type: 'text',
          content: content,
          length: content.length,
        };
        break;

      case 'image':
        messageResult = await sendWhatsAppImageMessage(
          phoneNumber,
          whatsappMediaId || mediaUrl, // Use media ID for uploaded files, URL for external links
          customer.whatsapp_phone_number_id,
          customer.system_access_token,
          caption,
          customer
        );
        sentMessageInfo = {
          type: 'image',
          mediaId: whatsappMediaId,
          mediaUrl: mediaUrl,
          caption: caption || '',
        };
        break;



      case 'video':
        messageResult = await sendWhatsAppVideoMessage(
          phoneNumber,
          whatsappMediaId || mediaUrl, // Use media ID for uploaded files, URL for external links
          customer.whatsapp_phone_number_id,
          customer.system_access_token,
          caption
        );
        sentMessageInfo = {
          type: 'video',
          mediaId: whatsappMediaId,
          mediaUrl: mediaUrl,
          caption: caption || '',
        };
        break;

      default:
        return res.status(400).json({
          error: "Invalid messageType. Supported types: text, image, video",
        });
    }

    // Update contact information
    await supabase.rpc("upsert_contact", {
      p_auth_id: authId,
      p_phone_number: phoneNumber,
    });

    // Track message statistics
    await Promise.all([
      supabase.from("message_statistics").insert([
        {
          auth_id: authId,
          phone_number: phoneNumber,
          message_type: "outgoing",
          message_length: sentMessageInfo.content?.length || 0,
          model_used: 'manual_send_file', // Distinguish from other manual sends
        },
      ]),
      // Increment outgoing message usage count
      supabase.rpc("increment_message_usage", {
        p_auth_id: authId,
        p_message_type: "outgoing",
      }),
    ]);

    // Store in chat history for context
    let chatContent = '';
    if (messageType === 'text') {
      chatContent = content;
    } else if (messageType === 'image') {
      chatContent = `[Sent an image${caption ? `: ${caption}` : ''}]`;
    } else if (messageType === 'video') {
      chatContent = `[Sent a video${caption ? `: ${caption}` : ''}]`;
    }

    await supabase.from("chat_history").insert([
      {
        auth_id: authId,
        phone_number: phoneNumber,
        role: "assistant",
        content: chatContent,
      },
    ]);

    // Update analytics
    const now = new Date();
    await supabase.rpc("update_monthly_statistics", {
      p_auth_id: authId,
      p_year: now.getFullYear(),
      p_month: now.getMonth() + 1,
    });

    res.json({
      success: true,
      message: "Message sent successfully",
      data: {
        whatsappMessageId: messageResult.messages?.[0]?.id,
        phoneNumber: phoneNumber,
        messageType: messageType,
        sentAt: new Date().toISOString(),
        ...sentMessageInfo,
      },
    });

  } catch (error) {
    console.error("Error sending WhatsApp message with file:", error);
    
    // Clean up uploaded file if there was an error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    res.status(500).json({ 
      error: "Failed to send message",
      details: error.message,
    });
  }
});

// List uploaded media files for a user
router.get("/uploaded-media", async (req, res) => {
  try {
    const { authId, mediaType, limit = 50, offset = 0 } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    let query = supabase
      .from("uploaded_media")
      .select("*")
      .eq("auth_id", authId)
      .order("uploaded_at", { ascending: false })
      .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    // Filter by media type if specified
    if (mediaType && ['image', 'video'].includes(mediaType)) {
      query = query.eq("media_type", mediaType);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Error getting uploaded media:", error);
      return res.status(500).json({ error: "Failed to get uploaded media" });
    }

    res.json({
      success: true,
      data: data || [],
      count: data?.length || 0,
    });

  } catch (error) {
    console.error("Error listing uploaded media:", error);
    res.status(500).json({ 
      error: "Failed to list uploaded media",
      details: error.message,
    });
  }
});

// Delete uploaded media file
router.delete("/uploaded-media/:filename", async (req, res) => {
  try {
    const { filename } = req.params;
    const { authId } = req.body;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required field: authId",
      });
    }

    // Get file info from database
    const { data: fileInfo, error: dbError } = await supabase
      .from("uploaded_media")
      .select("*")
      .eq("filename", filename)
      .eq("auth_id", authId)
      .single();

    if (dbError || !fileInfo) {
      return res.status(404).json({
        error: "File not found or you don't have permission to delete it",
      });
    }

    // Delete physical file
    const filePath = path.join(process.cwd(), 'uploads', filename);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Delete from database
    const { error: deleteError } = await supabase
      .from("uploaded_media")
      .delete()
      .eq("filename", filename)
      .eq("auth_id", authId);

    if (deleteError) {
      console.error("Error deleting from database:", deleteError);
      return res.status(500).json({ error: "Failed to delete file record" });
    }

    res.json({
      success: true,
      message: "File deleted successfully",
      data: {
        filename: filename,
        originalName: fileInfo.original_name,
        deletedAt: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error("Error deleting uploaded media:", error);
    res.status(500).json({ 
      error: "Failed to delete media file",
      details: error.message,
    });
  }
});

// Send bulk messages to multiple phone numbers
router.post("/send-bulk-message", async (req, res) => {
  try {
    const { 
      authId, 
      phoneNumbers, 
      messageType = 'text', 
      content, 
      imageUrl, 
      videoUrl,
      caption 
    } = req.body;

    // Validate required fields
    if (!authId || !phoneNumbers || !Array.isArray(phoneNumbers) || phoneNumbers.length === 0) {
      return res.status(400).json({
        error: "Missing required fields: authId, phoneNumbers (array)",
      });
    }

    // Limit bulk sending to prevent abuse
    if (phoneNumbers.length > 100) {
      return res.status(400).json({
        error: "Maximum 100 phone numbers allowed per bulk send",
      });
    }

    // Check user quotas before sending
    const { data: quotaCheck, error: quotaError } = await supabase.rpc(
      "check_user_quotas",
      { p_auth_id: authId },
    );

    if (quotaError) {
      return res.status(500).json({ error: "Failed to check user quota" });
    }

    const quotaInfo = quotaCheck[0];

    if (!quotaInfo || !quotaInfo.subscription_active) {
      return res.status(403).json({
        error: quotaInfo?.subscription_status === "expired"
          ? "Your subscription has expired. Please renew to continue using the chatbot."
          : "No active subscription found. Please subscribe to use the chatbot.",
      });
    }

    // Check if user has enough quota for bulk sending
    const remainingMessages = quotaInfo.message_limit === -1 ? 
      Infinity : 
      quotaInfo.message_limit - quotaInfo.current_message_count;

    if (remainingMessages < phoneNumbers.length) {
      return res.status(429).json({
        error: `Insufficient message quota. You need ${phoneNumbers.length} messages but only have ${remainingMessages} remaining.`,
      });
    }

    // Get customer WhatsApp configuration
    const { data: customer, error: customerError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .single();

    if (customerError || !customer) {
      return res.status(404).json({
        error: "WhatsApp configuration not found. Please set up your WhatsApp integration first.",
      });
    }

    const results = [];
    let successCount = 0;
    let failureCount = 0;

    // Send messages to each phone number
    for (const phoneNumber of phoneNumbers) {
      try {
        let messageResult;
        
                 // Send message based on type
         switch (messageType) {
           case 'text':
             messageResult = await sendWhatsAppTextMessage(
               phoneNumber,
               content,
               customer.whatsapp_phone_number_id,
               customer.system_access_token
             );
             break;
           case 'image':
             messageResult = await sendWhatsAppImageMessage(
               phoneNumber,
               imageUrl,
               customer.whatsapp_phone_number_id,
               customer.system_access_token,
               caption
             );
             break;

           case 'video':
             messageResult = await sendWhatsAppVideoMessage(
               phoneNumber,
               videoUrl,
               customer.whatsapp_phone_number_id,
               customer.system_access_token,
               caption
             );
             break;
           default:
             throw new Error(`Invalid messageType: ${messageType}`);
         }

        // Update contact and track statistics
        await Promise.all([
          supabase.rpc("upsert_contact", {
            p_auth_id: authId,
            p_phone_number: phoneNumber,
          }),
          supabase.from("message_statistics").insert([
            {
              auth_id: authId,
              phone_number: phoneNumber,
              message_type: "outgoing",
              message_length: content?.length || 0,
              model_used: 'bulk_send',
            },
          ]),
          supabase.rpc("increment_message_usage", {
            p_auth_id: authId,
            p_message_type: "outgoing",
          }),
        ]);

        results.push({
          phoneNumber,
          status: 'success',
          messageId: messageResult.messages?.[0]?.id,
        });
        
        successCount++;

        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`Failed to send to ${phoneNumber}:`, error);
        
        results.push({
          phoneNumber,
          status: 'failed',
          error: error.message,
        });
        
        failureCount++;
      }
    }

    // Update analytics
    const now = new Date();
    await supabase.rpc("update_monthly_statistics", {
      p_auth_id: authId,
      p_year: now.getFullYear(),
      p_month: now.getMonth() + 1,
    });

    res.json({
      success: true,
      message: `Bulk message completed. ${successCount} sent, ${failureCount} failed.`,
      data: {
        totalCount: phoneNumbers.length,
        successCount,
        failureCount,
        messageType,
        sentAt: new Date().toISOString(),
        results,
      },
    });

  } catch (error) {
    console.error("Error sending bulk WhatsApp messages:", error);
    res.status(500).json({ 
      error: "Failed to send bulk messages",
      details: error.message,
    });
  }
});

// Get API documentation for file upload endpoints
router.get("/file-upload-docs", async (req, res) => {
  try {
    const baseUrl = `${req.protocol}://${req.get('host')}/api/ai`;
    
    const documentation = {
      title: "WhatsApp File Upload & Messaging API",
      description: "Complete API for uploading and sending media files via WhatsApp",
      installation: {
        required_package: "multer",
        install_command: "npm install multer",
        note: "Make sure multer is installed for file upload functionality"
      },
      endpoints: {
        upload_media: {
          method: "POST",
          url: `${baseUrl}/upload-media`,
          description: "Upload images, audio, or video files",
          content_type: "multipart/form-data",
          fields: {
            media: "File (image/audio/video)",
            authId: "string (required)"
          },
          supported_formats: {
            images: ["jpeg", "jpg", "png", "gif", "webp"],
            audio: ["mp3", "wav", "ogg", "m4a", "aac"],
            video: ["mp4", "mpeg", "quicktime", "webm"]
          },
          max_file_size: "50MB",
          response_example: {
            success: true,
            message: "File uploaded successfully",
            data: {
              filename: "media-1641234567890-123456789.jpg",
              originalName: "my-image.jpg",
              mediaType: "image",
              mimeType: "image/jpeg",
              fileSize: 245760,
              fileUrl: `${baseUrl}/media/media-1641234567890-123456789.jpg`,
              uploadedAt: "2024-01-15T10:30:00Z"
            }
          }
        },
        send_message_with_file: {
          method: "POST",
          url: `${baseUrl}/send-message-with-file`,
          description: "Send WhatsApp message with uploaded file or external URL",
          content_type: "multipart/form-data",
          fields: {
            authId: "string (required)",
            phoneNumber: "string (required)",
            messageType: "text|image|audio|video (auto-detected if file uploaded)",
            content: "string (for text messages)",
            imageUrl: "string (for image messages)",
            audioUrl: "string (for audio messages)",
            videoUrl: "string (for video messages)",
            caption: "string (optional for image/video)",
            media: "File (optional - will auto-detect type)"
          }
        },
        send_message: {
          method: "POST",
          url: `${baseUrl}/send-message`,
          description: "Send WhatsApp message using URLs (no file upload)",
          content_type: "application/json",
          fields: {
            authId: "string (required)",
            phoneNumber: "string (required)",
            messageType: "text|image|audio|video",
            content: "string (for text)",
            imageUrl: "string (for image)",
            audioUrl: "string (for audio)",
            videoUrl: "string (for video)",
            caption: "string (optional)"
          }
        },
        list_uploaded_media: {
          method: "GET",
          url: `${baseUrl}/uploaded-media?authId=USER_ID&mediaType=image&limit=50&offset=0`,
          description: "List all uploaded media files for a user"
        },
        serve_media: {
          method: "GET",
          url: `${baseUrl}/media/:filename`,
          description: "Serve uploaded media files"
        },
        delete_media: {
          method: "DELETE",
          url: `${baseUrl}/uploaded-media/:filename`,
          description: "Delete uploaded media file",
          body: { authId: "string" }
        }
      },
      frontend_examples: {
        html_form: `
          <form id="uploadForm" enctype="multipart/form-data">
            <input type="hidden" name="authId" value="your-auth-id">
            <input type="file" name="media" accept="image/*,audio/*,video/*" required>
            <input type="text" name="phoneNumber" placeholder="+1234567890" required>
            <input type="text" name="caption" placeholder="Optional caption">
            <button type="submit">Upload & Send</button>
          </form>
        `,
        javascript_fetch: `
          // Upload and send file
          const formData = new FormData();
          formData.append('authId', 'your-auth-id');
          formData.append('phoneNumber', '+1234567890');
          formData.append('media', fileInput.files[0]);
          formData.append('caption', 'Check this out!');
          
          fetch('/api/ai/send-message-with-file', {
            method: 'POST',
            body: formData
          })
          .then(response => response.json())
          .then(data => console.log('Success:', data));
        `,
        react_example: `
          const sendFileMessage = async (file, phoneNumber, caption) => {
            const formData = new FormData();
            formData.append('authId', 'your-auth-id');
            formData.append('phoneNumber', phoneNumber);
            formData.append('media', file);
            if (caption) formData.append('caption', caption);
            
            try {
              const response = await fetch('/api/ai/send-message-with-file', {
                method: 'POST',
                body: formData
              });
              const result = await response.json();
              return result;
            } catch (error) {
              console.error('Error:', error);
            }
          };
        `
      },
      notes: {
        file_storage: "Files are stored locally in ./uploads directory",
        security: "Files are validated by type and size before upload",
        cleanup: "Failed uploads are automatically cleaned up",
        database: "File metadata is stored in 'uploaded_media' table (optional)",
        whatsapp_support: "WhatsApp supports images, audio, and video messages"
      }
    };

    res.json(documentation);
  } catch (error) {
    console.error("Error generating documentation:", error);
    res.status(500).json({ error: "Failed to generate documentation" });
  }
});

// Get order processing status
router.get("/settings/order-processing", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    // Get user profile and settings
    const [profileResult, settingsResult] = await Promise.all([
      supabase
        .from("profiles")
        .select("plan")
        .eq("auth_id", authId)
        .single(),
      supabase
        .from("chatbot_settings")
        .select("order_processing_enabled")
        .eq("auth_id", authId)
        .single(),
    ]);

    if (profileResult.error) {
      return res.status(500).json({ error: "Failed to check user profile" });
    }

    const isPaidPlan = profileResult.data && !['free', 'trial'].includes(profileResult.data.plan);
    const orderProcessingEnabled = settingsResult.data?.order_processing_enabled || false;

    res.json({
      success: true,
      data: {
        available: isPaidPlan,
        enabled: orderProcessingEnabled && isPaidPlan,
        plan: profileResult.data.plan,
        requiresUpgrade: !isPaidPlan,
      },
    });
  } catch (error) {
    console.error("Error getting order processing status:", error);
    res.status(500).json({ error: "Failed to get order processing status" });
  }
});

// Get order statistics
router.get("/orders/statistics", async (req, res) => {
  try {
    const { authId, startDate, endDate } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    let query = supabase
      .from("orders")
      .select("order_status, created_at")
      .eq("auth_id", authId);

    if (startDate) {
      query = query.gte("created_at", startDate);
    }
    if (endDate) {
      query = query.lte("created_at", endDate);
    }

    const { data, error } = await query;

    if (error) throw error;

    // Calculate statistics
    const stats = {
      total: data.length,
      pending: data.filter(order => order.order_status === 'pending').length,
      confirmed: data.filter(order => order.order_status === 'confirmed').length,
      processing: data.filter(order => order.order_status === 'processing').length,
      shipped: data.filter(order => order.order_status === 'shipped').length,
      delivered: data.filter(order => order.order_status === 'delivered').length,
      cancelled: data.filter(order => order.order_status === 'cancelled').length,
      byStatus: {},
    };

    // Group by status
    stats.byStatus = data.reduce((acc, order) => {
      acc[order.order_status] = (acc[order.order_status] || 0) + 1;
      return acc;
    }, {});

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error("Error getting order statistics:", error);
    res.status(500).json({ error: "Failed to get order statistics" });
  }
});

// Order/Booking Tracking from Google Sheets

// Track order by phone number
router.get("/orders/track", async (req, res) => {
  try {
    const { authId, phoneNumber, orderId } = req.query;

    if (!authId || !phoneNumber) {
      return res.status(400).json({
        error: "Missing required parameters: authId, phoneNumber",
      });
    }

    // Get customer's WhatsApp configuration
    const { data: customer, error: customerError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .single();

    if (customerError || !customer) {
      return res.status(404).json({
        error: "Customer configuration not found",
      });
    }

    if (!customer.spreadsheet_id) {
      return res.status(400).json({
        error: "Google Sheets not configured for this customer",
      });
    }

    // Get order status from Google Sheets
    const orderStatus = await getOrderStatusFromGoogleSheets(customer, phoneNumber, orderId);

    res.json({
      success: true,
      data: orderStatus,
    });
  } catch (error) {
    console.error("Error tracking order:", error);
    res.status(500).json({ error: "Failed to track order" });
  }
});

// Get all orders from Google Sheets for a customer
router.get("/orders/sheets-data", async (req, res) => {
  try {
    const { authId, phoneNumber } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    // Get customer's WhatsApp configuration
    const { data: customer, error: customerError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .single();

    if (customerError || !customer) {
      return res.status(404).json({
        error: "Customer configuration not found",
      });
    }

    if (!customer.spreadsheet_id) {
      return res.status(400).json({
        error: "Google Sheets not configured for this customer",
      });
    }

    // Read all data from Google Sheets
    const sheetData = await readFromGoogleSheets(customer);

    let filteredData = sheetData;

    // Filter by phone number if provided
    if (phoneNumber && sheetData.length > 0) {
      const headers = sheetData[0];
      const rows = sheetData.slice(1);

      const phoneColumnIndex = headers.findIndex(header =>
        header.toLowerCase().includes('phone') ||
        header.toLowerCase().includes('number') ||
        header.toLowerCase().includes('contact')
      );

      if (phoneColumnIndex !== -1) {
        const matchingRows = rows.filter(row => {
          const rowPhoneNumber = row[phoneColumnIndex];
          const cleanRowPhone = rowPhoneNumber ? rowPhoneNumber.replace(/\D/g, '') : '';
          const cleanSearchPhone = phoneNumber.replace(/\D/g, '');
          return cleanRowPhone.includes(cleanSearchPhone) || cleanSearchPhone.includes(cleanRowPhone);
        });

        filteredData = [headers, ...matchingRows];
      }
    }

    res.json({
      success: true,
      data: {
        headers: filteredData.length > 0 ? filteredData[0] : [],
        rows: filteredData.length > 1 ? filteredData.slice(1) : [],
        totalRows: filteredData.length - 1,
        spreadsheetId: customer.spreadsheet_id,
        worksheetName: customer.worksheet_name,
      },
    });
  } catch (error) {
    console.error("Error getting sheets data:", error);
    res.status(500).json({ error: "Failed to get sheets data" });
  }
});

// Booking Reminders from Google Sheets

// Process booking reminders for a specific customer
router.post("/booking-reminders/process", async (req, res) => {
  try {
    const { authId } = req.body;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required field: authId",
      });
    }

    // Get customer's WhatsApp configuration
    const { data: customer, error: customerError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .single();

    if (customerError || !customer) {
      return res.status(404).json({
        error: "Customer configuration not found",
      });
    }

    // Import and use the booking reminder processor
    const { processBookingRemindersFromSheets } = await import('../utils/followUpScheduler.js');
    const result = await processBookingRemindersFromSheets(authId, customer);

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error processing booking reminders:", error);
    res.status(500).json({ error: "Failed to process booking reminders" });
  }
});

// Process booking reminders for all customers
router.post("/booking-reminders/process-all", async (req, res) => {
  try {
    // Get all customers with Google Sheets configured
    const { data: customers, error: customersError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .not("spreadsheet_id", "is", null);

    if (customersError) {
      return res.status(500).json({
        error: "Failed to fetch customers",
      });
    }

    const results = [];
    const { processBookingRemindersFromSheets } = await import('../utils/followUpScheduler.js');

    // Process each customer
    for (const customer of customers) {
      try {
        const result = await processBookingRemindersFromSheets(customer.auth_id, customer);
        results.push({
          authId: customer.auth_id,
          result
        });
      } catch (error) {
        console.error(`Error processing booking reminders for ${customer.auth_id}:`, error);
        results.push({
          authId: customer.auth_id,
          result: { success: false, error: error.message }
        });
      }
    }

    const successCount = results.filter(r => r.result.success).length;
    const totalReminders = results.reduce((sum, r) => sum + (r.result.remindersProcessed || 0), 0);

    res.json({
      success: true,
      data: {
        customersProcessed: customers.length,
        successfulCustomers: successCount,
        totalRemindersScheduled: totalReminders,
        results
      },
    });
  } catch (error) {
    console.error("Error processing all booking reminders:", error);
    res.status(500).json({ error: "Failed to process all booking reminders" });
  }
});

// Human Takeover Management Routes

// Start a human takeover session
router.post("/human-takeover/start", async (req, res) => {
  try {
    const { authId, phoneNumber, timeoutMinutes = 30 } = req.body;

    if (!authId || !phoneNumber) {
      return res.status(400).json({
        error: "Missing required fields: authId, phoneNumber",
      });
    }

    // Start the human takeover session
    const { data: sessionId, error } = await supabase.rpc(
      "start_human_takeover_session",
      { 
        p_auth_id: authId, 
        p_phone_number: phoneNumber, 
        p_timeout_minutes: timeoutMinutes 
      }
    );

    if (error) {
      console.error("Error starting human takeover session:", error);
      return res.status(500).json({ error: "Failed to start human takeover session" });
    }

    console.log(`👤 Human takeover session started | Auth: ${authId} | Phone: ${phoneNumber} | Session: ${sessionId}`);

    res.json({
      success: true,
      data: {
        sessionId,
        authId,
        phoneNumber,
        timeoutMinutes,
        message: "Human takeover session started. AI responses are now paused for this customer.",
      },
    });
  } catch (error) {
    console.error("Error starting human takeover session:", error);
    res.status(500).json({ error: "Failed to start human takeover session" });
  }
});

// End a human takeover session
router.post("/human-takeover/end", async (req, res) => {
  try {
    const { authId, phoneNumber } = req.body;

    if (!authId || !phoneNumber) {
      return res.status(400).json({
        error: "Missing required fields: authId, phoneNumber",
      });
    }

    // End the human takeover session
    const { data: sessionEnded, error } = await supabase.rpc(
      "end_human_takeover_session",
      { p_auth_id: authId, p_phone_number: phoneNumber }
    );

    if (error) {
      console.error("Error ending human takeover session:", error);
      return res.status(500).json({ error: "Failed to end human takeover session" });
    }

    console.log(`👤 Human takeover session ended | Auth: ${authId} | Phone: ${phoneNumber} | Found: ${sessionEnded}`);

    res.json({
      success: true,
      data: {
        sessionEnded,
        authId,
        phoneNumber,
        message: sessionEnded 
          ? "Human takeover session ended. AI responses have resumed for this customer."
          : "No active human takeover session found for this customer.",
      },
    });
  } catch (error) {
    console.error("Error ending human takeover session:", error);
    res.status(500).json({ error: "Failed to end human takeover session" });
  }
});

// Check if human takeover is active
router.get("/human-takeover/status", async (req, res) => {
  try {
    const { authId, phoneNumber } = req.query;

    if (!authId || !phoneNumber) {
      return res.status(400).json({
        error: "Missing required parameters: authId, phoneNumber",
      });
    }

    // Check if human takeover is active
    const { data: isActive, error } = await supabase.rpc(
      "is_human_takeover_active",
      { p_auth_id: authId, p_phone_number: phoneNumber }
    );

    if (error) {
      console.error("Error checking human takeover status:", error);
      return res.status(500).json({ error: "Failed to check human takeover status" });
    }

    // Get session details if active
    let sessionDetails = null;
    if (isActive) {
      const { data: session, error: sessionError } = await supabase
        .from("human_takeover_sessions")
        .select("*")
        .eq("auth_id", authId)
        .eq("phone_number", phoneNumber)
        .eq("is_active", true)
        .single();

      if (!sessionError && session) {
        sessionDetails = {
          sessionId: session.id,
          startedAt: session.started_at,
          lastActivityAt: session.last_activity_at,
          timeoutMinutes: session.timeout_minutes,
        };
      }
    }

    res.json({
      success: true,
      data: {
        isActive,
        authId,
        phoneNumber,
        session: sessionDetails,
        message: isActive 
          ? "Human takeover is active. AI responses are paused."
          : "Human takeover is not active. AI responses are enabled.",
      },
    });
  } catch (error) {
    console.error("Error checking human takeover status:", error);
    res.status(500).json({ error: "Failed to check human takeover status" });
  }
});

// Update human takeover session activity (called when human sends a message)
router.post("/human-takeover/activity", async (req, res) => {
  try {
    const { authId, phoneNumber } = req.body;

    if (!authId || !phoneNumber) {
      return res.status(400).json({
        error: "Missing required fields: authId, phoneNumber",
      });
    }

    // Update the session activity
    const { data: sessionFound, error } = await supabase.rpc(
      "update_human_takeover_activity",
      { p_auth_id: authId, p_phone_number: phoneNumber }
    );

    if (error) {
      console.error("Error updating human takeover activity:", error);
      return res.status(500).json({ error: "Failed to update human takeover activity" });
    }

    res.json({
      success: true,
      data: {
        sessionFound,
        authId,
        phoneNumber,
        message: sessionFound 
          ? "Human takeover session activity updated."
          : "No active human takeover session found.",
      },
    });
  } catch (error) {
    console.error("Error updating human takeover activity:", error);
    res.status(500).json({ error: "Failed to update human takeover activity" });
  }
});

// Get all human takeover sessions for a user
router.get("/human-takeover/sessions", async (req, res) => {
  try {
    const { authId, limit = 50, offset = 0 } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    const { data: sessions, error } = await supabase
      .from("human_takeover_sessions")
      .select("*")
      .eq("auth_id", authId)
      .order("created_at", { ascending: false })
      .limit(parseInt(limit))
      .offset(parseInt(offset));

    if (error) {
      console.error("Error fetching human takeover sessions:", error);
      return res.status(500).json({ error: "Failed to fetch human takeover sessions" });
    }

    res.json({
      success: true,
      data: {
        sessions,
        count: sessions.length,
        authId,
      },
    });
  } catch (error) {
    console.error("Error fetching human takeover sessions:", error);
    res.status(500).json({ error: "Failed to fetch human takeover sessions" });
  }
});

// Helper function to upload media to WhatsApp servers
async function uploadMediaToWhatsApp(buffer, mimeType, phoneNumberId, accessToken) {
  try {
    console.log(`📤 Uploading media to WhatsApp | PhoneID: ${phoneNumberId} | Type: ${mimeType}`);

    // Validate buffer
    if (!buffer || buffer.length === 0) {
      throw new Error('Empty media buffer received');
    }

    // Validate file size (WhatsApp limit is 16MB)
    if (buffer.length > 16 * 1024 * 1024) {
      throw new Error('File size exceeds WhatsApp 16MB limit');
    }

    // Validate minimum file size (1KB)
    if (buffer.length < 1024) {
      throw new Error('File size too small, minimum 1KB required');
    }

    // For WebP images, suggest converting to JPEG/PNG
    if (mimeType === 'image/webp') {
      console.warn('⚠️ Warning: WebP format is not recommended. Consider converting to JPEG or PNG for better compatibility.');
    }

    // Create form data for upload
    const formData = new FormData();
    formData.append('messaging_product', 'whatsapp');
    // FIX: Use Buffer instead of Blob for Node.js compatibility
    formData.append('file', buffer, { filename: 'media', contentType: mimeType });

    // Add explicit headers for better compatibility
    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'multipart/form-data',
      'Accept': 'application/json',
    };

    const response = await axios.post(
      `https://graph.facebook.com/v18.0/${phoneNumberId}/media`,
      formData,
      {
        headers,
        maxContentLength: 16 * 1024 * 1024, // 16MB limit
        timeout: 30000, // 30 second timeout
      }
    );

    if (!response.data || !response.data.id) {
      throw new Error('Invalid response from WhatsApp API: Missing media ID');
    }

    // Verify the media was uploaded
    try {
      await axios.get(
        `https://graph.facebook.com/v18.0/${response.data.id}`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Accept': 'application/json',
          },
          timeout: 10000,
        }
      );
    } catch (verifyError) {
      throw new Error(`Media upload succeeded but verification failed: ${verifyError.message}`);
    }

    return response.data.id;
  } catch (error) {
    console.error('❌ Error uploading media to WhatsApp:', error.response?.data || error.message);
    throw new Error(`Failed to upload media: ${error.response?.data?.error?.message || error.message}`);
  }
}

// Helper function to prepare audio for Whisper with enhanced format support
function prepareAudioForWhisper(audioBuffer, mimeType) {
  try {
    logger.debug(`Preparing audio for Whisper | Size: ${audioBuffer.length} bytes`);
    
    // Validate audio buffer
    if (!audioBuffer || audioBuffer.length === 0) {
      throw new Error('Empty audio buffer received');
    }

    // File size validation
    const maxSize = 25 * 1024 * 1024; // 25MB (OpenAI's limit)
    if (audioBuffer.length > maxSize) {
      throw new Error(`Audio file too large: ${audioBuffer.length} bytes. Maximum allowed: ${maxSize} bytes`);
    }

    // Minimum size validation
    if (audioBuffer.length < 1024) {
      throw new Error('Audio file too small (minimum 1KB required)');
    }

    // Enhanced MIME type to extension mapping
    const audioExtensions = {
      'audio/mpeg': '.mp3',
      'audio/mp3': '.mp3',
      'audio/wav': '.wav',
      'audio/wave': '.wav',
      'audio/x-wav': '.wav',
      'audio/vnd.wave': '.wav',
      'audio/m4a': '.m4a',
      'audio/mp4': '.m4a',
      'audio/aac': '.aac',
      'audio/x-aac': '.aac',
      'audio/ogg': '.ogg',
      'audio/opus': '.ogg',
      'audio/webm': '.webm',
      'audio/flac': '.flac',
      'audio/x-flac': '.flac',
      'audio/amr': '.amr',
      'audio/3gpp': '.3gp',
      'audio/3gpp2': '.3gp2'
    };

    // Get file extension, default to .ogg for WhatsApp voice messages
    const extension = audioExtensions[mimeType] || '.ogg';

    // Generate secure temporary filename
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const tempFilePath = path.join(os.tmpdir(), `whatsapp_voice_${timestamp}_${randomId}${extension}`);

    // Write buffer to temporary file with error handling
    try {
      fs.writeFileSync(tempFilePath, audioBuffer);
      console.log(`📁 Audio file created: ${tempFilePath}`);
    } catch (writeError) {
      throw new Error(`Failed to write audio file: ${writeError.message}`);
    }

    // Verify file integrity
    const stats = fs.statSync(tempFilePath);
    if (stats.size === 0) {
      throw new Error('Created audio file is empty');
    }

    if (stats.size !== audioBuffer.length) {
      console.warn(`⚠️ File size mismatch: Expected ${audioBuffer.length}, got ${stats.size}`);
    }

    logger.debug(`Audio file prepared successfully | Size: ${stats.size} bytes`);
    return tempFilePath;

  } catch (error) {
    console.error('❌ Error preparing audio for Whisper:', error);
    throw new Error(`Audio preparation failed: ${error.message}`);
  }
}

// Enhanced voice message processing with OpenAI Whisper
async function processVoiceMessageWithAI(audioBuffer, mimeType) {
  const processingStartTime = Date.now();
  let tempFilePath = null;
  
  try {
    logger.debug(`Starting voice message processing | Size: ${audioBuffer.length} bytes`);
    
    // Prepare audio file for processing
    tempFilePath = prepareAudioForWhisper(audioBuffer, mimeType);
    
    logger.debug(`Sending to OpenAI Whisper | File: ${path.basename(tempFilePath)}`);
    
        // Verify file exists before processing
    if (!fs.existsSync(tempFilePath)) {
      throw new Error(`Temporary audio file not found: ${tempFilePath}`);
    }
    
    const fileStats = fs.statSync(tempFilePath);
    logger.debug(`Preparing audio file for Whisper API: ${tempFilePath} (${fileStats.size} bytes)`);
    
    // Use form-data to create a proper multipart form for OpenAI API
    const formData = new FormData();
    formData.append('file', fs.createReadStream(tempFilePath), {
      filename: path.basename(tempFilePath),
      contentType: mimeType || 'audio/ogg'
    });
    formData.append('model', 'whisper-1');
    // No language parameter - let Whisper auto-detect the language (supports 99+ languages)
    formData.append('response_format', 'json'); // Use JSON to get language detection info
    formData.append('temperature', '0.1');
    formData.append('prompt', 'Malaysian customer voice message. Transcribe accurately with Malaysian English patterns: particles (lah, lor, meh, ah), code-switching, slang (alamak, aiyo, wah), colloquial expressions, product names, numbers.');
    
    logger.debug(`Sending multipart form to OpenAI Whisper API`);
    
    // Make direct HTTP request to OpenAI API
    const response = await axios.post(
      'https://api.openai.com/v1/audio/transcriptions',
      formData,
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          ...formData.getHeaders()
        },
        timeout: 30000, // 30 second timeout
        maxContentLength: 25 * 1024 * 1024, // 25MB max (OpenAI limit)
      }
    );
    
    const processingTime = Date.now() - processingStartTime;

    // Extract transcription data from JSON response
    const transcriptionData = response.data;
    const transcriptionText = transcriptionData.text || '';
    const detectedLanguage = transcriptionData.language || 'unknown';
    const duration = transcriptionData.duration || 0;
    
    // Clean up temporary file after successful transcription
    cleanupTempFile(tempFilePath);
    
    return {
      text: transcriptionText,
      language: detectedLanguage,
      duration: duration,
      confidence: 0,
      segments: transcriptionData.segments || [],
      processingTime: processingTime
    };

  } catch (error) {
    const processingTime = Date.now() - processingStartTime;
    console.error(`❌ Voice message processing failed after ${processingTime}ms:`, error);
    
    // Enhanced error details
    const errorDetails = {
      message: error.message,
      mimeType: mimeType,
      bufferSize: audioBuffer?.length || 0,
      tempFilePath: tempFilePath,
      processingTime: processingTime,
      errorType: error.constructor.name
    };
    
    console.error('🔍 Error details:', errorDetails);
    
    // Clean up on error
    if (tempFilePath) {
      cleanupTempFile(tempFilePath);
    }
    
    throw new Error(`Voice message processing failed: ${error.message}`);
  }
}

// Enhanced cleanup function
function cleanupTempFile(filePath) {
  try {
    if (filePath && fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`🗑️ Temporary file cleaned up: ${filePath}`);
    }
  } catch (cleanupError) {
    console.error(`⚠️ Error cleaning up temporary file ${filePath}:`, cleanupError);
  }
}

// Enhanced media content processing with better voice message handling
async function processMediaContentEnhanced(messageContent, messageType, accessToken, authId = null, phoneNumber = null) {
  const processingStartTime = Date.now();
  
  try {
    console.log(`🎯 Processing ${messageType} content | Media ID: ${messageContent.id}`);
    console.log(`📎 Media content details:`, {
      id: messageContent.id,
      mime_type: messageContent.mime_type,
      filename: messageContent.filename,
      sha256: messageContent.sha256
    });

    // Download media from WhatsApp
    const mediaData = await downloadWhatsAppMedia(messageContent.id, accessToken);
    
    // Save media file for chat history (if authId and phoneNumber provided)
    let savedMedia = null;
    if (authId && phoneNumber) {
      savedMedia = await saveWhatsAppMediaForHistory(mediaData, messageType, authId, phoneNumber);
    }
    
    let processedText = '';
    let rawTranscription = ''; // Store raw transcription for better knowledge base search
    
    switch (messageType) {
      case 'image':
        console.log(`🖼️ Processing image with AI vision | Size: ${mediaData.buffer.length} bytes | MIME: ${mediaData.mimeType}`);
        const imageDescription = await processImageWithAI(mediaData.buffer, mediaData.mimeType);
        processedText = `[Customer sent an image] ${imageDescription}`;
        rawTranscription = imageDescription; // For images, the description is the searchable content
        console.log(`🖼️ Image processing complete | Description length: ${imageDescription.length} chars`);
        break;
        
      case 'audio':
        console.log(`🎤 Processing voice message with enhanced AI`);
        
        const voiceResult = await processVoiceMessageWithAI(
          mediaData.buffer, 
          mediaData.mimeType
        );
       
        // Enhanced processed text with metadata including language info
        processedText = `[Customer sent a voice message in ${voiceResult.language}] ${voiceResult.text}`;
        rawTranscription = voiceResult.text; // Store raw transcription for knowledge base search
        
        break;
        
      case 'video':
        console.log(`🎬 Processing video content`);
        processedText = `[Customer sent a video] Video received and saved. You can ask the customer to describe what they wanted to show in the video.`;
        rawTranscription = 'Customer sent a video'; // Simple searchable text for videos
        break;
        
      default:
        console.log(`📄 Processing ${messageType} content`);
        processedText = `[Customer sent a ${messageType}] Media file received and saved.`;
        rawTranscription = `Customer sent a ${messageType}`;
    }
    
    const processingTime = Date.now() - processingStartTime;
    console.log(`✅ Media processing completed in ${processingTime}ms | Type: ${messageType} | Result length: ${processedText.length}`);
    
    return {
      processedText: processedText,
      rawTranscription: rawTranscription, // Add raw transcription for better knowledge base search
      mediaInfo: savedMedia,
      processingTime: processingTime
    };
    
  } catch (error) {
    const processingTime = Date.now() - processingStartTime;
    console.error(`❌ Media processing failed after ${processingTime}ms:`, error);
    console.error(`❌ Error details:`, {
      messageType,
      mediaId: messageContent.id,
      mimeType: messageContent.mime_type,
      filename: messageContent.filename,
      hasAccessToken: !!accessToken,
      errorMessage: error.message,
      errorStack: error.stack
    });
    
    // Enhanced fallback messages
    const fallbackMessages = {
      image: "[Customer sent an image, but it couldn't be processed. Please ask them to describe what they wanted to show or send the image again.]",
      audio: "[Customer sent a voice message, but it couldn't be processed. Please ask them to send a text message or try recording the voice message again.]",
      video: "[Customer sent a video, but it couldn't be processed. Please ask them to describe what they wanted to show or send the video again.]"
    };
    
    const fallbackText = fallbackMessages[messageType] || 
      `[Customer sent a ${messageType} file, but it couldn't be processed. Please ask them to try again or send a text message instead.]`;
    
    return {
      processedText: fallbackText,
      rawTranscription: '', // Empty raw transcription for failed processing
      mediaInfo: null,
      processingTime: processingTime,
      error: error.message
    };
  }
}

// Enhanced voice message handling in incoming message processing
async function processIncomingMessageEnhanced(
  customer,
  fromNumber,
  messageContent,
  phoneNumberId,
  messageType = 'text',
) {
  const processingStartTime = Date.now();
  let mediaResult = null;
  
  try {
    console.log(`🔄 Processing incoming ${messageType} message | From: ${fromNumber} | Customer: ${customer.auth_id}`);
    
    // Process message content
    let processedMessage = messageContent;
    
    if (messageType !== 'text') {
      // Get customer's access token for media download
      const { data: customerConfig, error: configError } = await supabase
        .from("whatsapp_customers")
        .select("system_access_token")
        .eq("auth_id", customer.auth_id)
        .single();

      if (configError || !customerConfig?.system_access_token) {
        console.error("Error getting customer access token for media:", configError);
        
        // Send appropriate error message based on media type
        const errorMessages = {
          audio: "Sorry, I'm having trouble processing your voice message. Please try sending a text message instead or record your voice message again.",
          image: "Sorry, I'm having trouble processing your image. Please try sending a text message instead or send the image again.",
          video: "Sorry, I'm having trouble processing your video. Please try sending a text message instead or send the video again."
        };
        
        await sendWhatsAppMessage(
          fromNumber,
          errorMessages[messageType] || "Sorry, I'm having trouble processing your media message. Please try sending a text message instead.",
          phoneNumberId,
        );
        return;
      }

      // Process the media content with enhanced processing
      mediaResult = await processMediaContentEnhanced(
        messageContent,
        messageType,
        customerConfig.system_access_token,
        customer.auth_id,
        fromNumber
      );
      
      processedMessage = mediaResult.processedText;
      
      console.log(`🔄 ${messageType} processed | Duration: ${mediaResult.processingTime}ms | Result: ${processedMessage.substring(0, 100)}...`);
      
      // Log any processing errors
      if (mediaResult.error) {
        console.error(`⚠️ Media processing had errors: ${mediaResult.error}`);
      }
    }

    // Check user quotas before processing
    const { data: quotaCheck, error: quotaError } = await supabase.rpc(
      "check_user_quotas",
      { p_auth_id: customer.auth_id },
    );

    if (quotaError) {
      console.error("Error checking quotas:", quotaError);
      return;
    }

    const quotaInfo = quotaCheck[0];

    // Handle subscription and quota checks
    if (!quotaInfo || !quotaInfo.subscription_active) {
      const errorMessage = quotaInfo?.subscription_status === "expired"
        ? "Your subscription has expired. Please renew to continue using the chatbot."
        : "No active subscription found. Please subscribe to use the chatbot.";

      await supabase.from("quota_violations").insert({
        auth_id: customer.auth_id,
        violation_type: "subscription_expired",
        attempted_action: "whatsapp_message",
        current_usage: quotaInfo?.current_message_count || 0,
        limit_value: quotaInfo?.message_limit || 0,
        plan_name: "Unknown",
      });
      return;
    }

    if (!quotaInfo.can_send_message) {
      const errorMessage = `You have reached your monthly message limit (${quotaInfo.current_message_count}/${quotaInfo.message_limit}). Please upgrade your plan or wait for next month.`;
      await sendWhatsAppMessage(fromNumber, errorMessage, phoneNumberId);

      await supabase.from("quota_violations").insert({
        auth_id: customer.auth_id,
        violation_type: "message_limit",
        attempted_action: "whatsapp_message",
        current_usage: quotaInfo.current_message_count,
        limit_value: quotaInfo.message_limit,
        plan_name: "Current Plan",
      });
      return;
    }

    // Continue with existing message processing logic...
    // Get chatbot settings to check if order processing is enabled
    const { data: settings, error: settingsError } = await supabase
      .from("chatbot_settings")
      .select("order_processing_enabled")
      .eq("auth_id", customer.auth_id)
      .single();

    // Check if user has a paid plan for order processing
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("plan")
      .eq("auth_id", customer.auth_id)
      .single();

    const isPaidPlan = profile && !['free', 'trial'].includes(profile.plan);
    const orderProcessingEnabled = settings?.order_processing_enabled && isPaidPlan;

    // Route to appropriate processing
    if (orderProcessingEnabled) {
      console.log(`🛒 Routing to ORDER PROCESSING mode | Customer: ${customer.auth_id} | Phone: ${fromNumber} | Message: "${processedMessage}"`);

      const { data: existingOrder, error: orderError } = await supabase
        .from("orders")
        .select("*")
        .eq("auth_id", customer.auth_id)
        .eq("phone_number", fromNumber)
        .eq("order_status", "pending")
        .order("created_at", { ascending: false })
        .limit(1);

      if (orderError) {
        console.error("Error checking existing orders:", orderError);
      }

      console.log(`📋 Existing pending order: ${existingOrder?.[0] ? `Order ID ${existingOrder[0].id}` : 'None'}`);

      await handleOrderProcessing(customer, fromNumber, processedMessage, phoneNumberId, existingOrder?.[0] || null);
    } else {
      console.log(`💬 Routing to REGULAR CHAT mode | Customer: ${customer.auth_id} | Phone: ${fromNumber} | Message: "${processedMessage}"`);
      await handleRegularChat(customer, fromNumber, processedMessage, phoneNumberId);
    }

    // Store enhanced media information in chat history
    if (messageType !== 'text' && mediaResult && mediaResult.mediaInfo) {
      try {
        const { error: updateError } = await supabase
          .from("chat_history")
          .update({
            media_filename: mediaResult.mediaInfo.filename,
            media_url: mediaResult.mediaInfo.fileUrl,
            media_type: mediaResult.mediaInfo.mediaType,
            mime_type: mediaResult.mediaInfo.mimeType,
            file_size: mediaResult.mediaInfo.fileSize
          })
          .eq("auth_id", customer.auth_id)
          .eq("phone_number", fromNumber)
          .eq("role", "user")
          .order("created_at", { ascending: false })
          .limit(1);

        if (updateError) {
          console.error("Error updating chat history with media info:", updateError);
        }
      } catch (error) {
        console.error("Error storing enhanced media info in chat history:", error);
      }
    }

  } catch (error) {
    const processingTime = Date.now() - processingStartTime;
    console.error(`❌ Enhanced message processing error | From: ${fromNumber} | Duration: ${processingTime}ms | Error:`, error);
    
    // Enhanced error messages based on message type
    const errorMessages = {
      text: "Sorry, I encountered an error processing your message. Please try again.",
      image: "Sorry, I had trouble processing your image. Please try sending it again or describe what you wanted to show me.",
      audio: "Sorry, I had trouble processing your voice message. Please try recording it again or send a text message instead.",
      video: "Sorry, I had trouble processing your video. Please try sending it again or describe what you wanted to show me."
    };
    
    await sendWhatsAppMessage(fromNumber, errorMessages[messageType], phoneNumberId);
  }
}

// Helper function to intelligently send images from knowledge base matches
async function sendKnowledgeBaseImages(authId, phoneNumber, matchingSections, customerMessage = '') {
  if (!matchingSections || matchingSections.length === 0) {
    return;
  }

  // Filter sections that have images
  const sectionsWithImages = matchingSections.filter(section =>
    section.has_image && section.image_url
  );

  if (sectionsWithImages.length === 0) {
    console.log(`📸 No images found in knowledge base matches for ${phoneNumber}`);
    return;
  }

  console.log(`📸 Found ${sectionsWithImages.length} knowledge base entries with images for ${phoneNumber}`);

  try {
    // Use AI to determine if images are appropriate for this context
    const shouldSendImages = await shouldSendKnowledgeBaseImages(customerMessage, sectionsWithImages);

    if (!shouldSendImages) {
      console.log(`🤖 AI determined images not appropriate for context: "${customerMessage}"`);
      return;
    }

    // Get customer WhatsApp configuration
    const { data: customer, error: customerError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .single();

    if (customerError || !customer) {
      console.error("Error getting WhatsApp config for auto-image sending:", customerError);
      return;
    }

    // Limit to maximum 2 images to avoid overwhelming
    const imagesToSend = sectionsWithImages.slice(0, 2);

    // Send images sequentially to avoid overwhelming the customer
    for (const section of imagesToSend) {
      try {
        console.log(`📤 Sending image from knowledge base: ${section.title} (${section.image_filename})`);

        // Send the image message
        const messageResult = await sendWhatsAppImageMessage(
          phoneNumber,
          section.image_url,
          customer.whatsapp_phone_number_id,
          customer.system_access_token,
          `📸 ${section.title}`, // Simple caption with the title
          customer
        );

        // Store the sent message in chat history
        await supabase.from("chat_history").insert({
          auth_id: authId,
          phone_number: phoneNumber,
          role: "assistant",
          content: `📸 ${section.title}`,
          media_filename: section.image_filename,
          media_url: section.image_url,
          media_type: 'image',
          mime_type: section.image_mime_type,
        });

        // Update usage statistics for the image message
        await supabase.rpc("increment_message_usage", {
          p_auth_id: authId,
          p_message_type: "outgoing",
        });

        console.log(`✅ Image sent successfully: ${section.title}`);

        // Small delay between images to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (imageError) {
        console.error(`❌ Error sending image for ${section.title}:`, imageError);
        // Continue with other images even if one fails
      }
    }

    // Track analytics for auto-sent images
    if (imagesToSend.length > 0) {
      await updateAnalytics(authId, phoneNumber, {
        messageType: 'outgoing',
        knowledgeQuery: false,
        knowledgeHit: true,
        knowledgeSectionsFound: imagesToSend.length,
        similarityScore: 0,
        responseTime: 0,
        totalTokens: 0,
        promptTokens: 0,
        completionTokens: 0
      });
    }

  } catch (error) {
    console.error("Error in auto-sending knowledge base images:", error);
  }
}

// AI-powered function to determine if images should be sent based on context
async function shouldSendKnowledgeBaseImages(customerMessage, sectionsWithImages) {
  try {
    // Don't send images for very basic greetings or simple questions
    const basicGreetings = /^(hi|hello|hey|good morning|good afternoon|good evening|thanks|thank you|ok|okay|yes|no|ya|不是|是|好|谢谢|你好|嗨)$/i;
    if (basicGreetings.test(customerMessage.trim())) {
      return false;
    }

    // Check if customer is explicitly asking for visual content
    const visualKeywords = /\b(show|see|picture|image|photo|look|view|display|visual|pic|img|pics|images|photos|看|显示|图片|照片|相片|gambar|foto|tengok|lihat|papar)\b/i;
    if (visualKeywords.test(customerMessage)) {
      return true;
    }

    // Use AI to make intelligent decision
    const imageDecisionResponse = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: `Determine if sending images would be helpful and appropriate for the customer's message.

SEND IMAGES (reply "YES") when:
- Customer asks about products, services, or specific items
- Customer wants recommendations or suggestions
- Customer asks "what do you have" or similar
- Customer is inquiring about visual aspects
- Customer seems to be browsing or exploring options

DON'T SEND IMAGES (reply "NO") when:
- Customer is just greeting or being polite
- Customer is asking about prices, delivery, or contact info only
- Customer is placing a specific order (already knows what they want)
- Customer is asking about policies, hours, or general info
- Customer message is very short or unclear
- Customer is asking about order status or tracking

Reply only "YES" or "NO".`
        },
        {
          role: "user",
          content: `Customer message: "${customerMessage}"
Available images: ${sectionsWithImages.map(s => s.title).join(', ')}
Should I send images?`
        }
      ],
      max_tokens: 10,
      temperature: 0.1,
    });

    const decision = imageDecisionResponse.choices[0].message.content.trim().toUpperCase();
    console.log(`🤖 AI image decision for "${customerMessage}": ${decision}`);

    return decision === 'YES';

  } catch (error) {
    console.error("Error in AI image decision:", error);
    // Fallback: be conservative and don't send images
    return false;
  }
}

// ===== AUTO-TAGGING AND FOLLOW-UP ROUTES =====

// Trigger AI analysis for a specific contact
router.post("/contacts/:contactId/analyze", async (req, res) => {
  try {
    const { contactId } = req.params;
    const { authId, triggeredBy = 'manual_trigger' } = req.body;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required field: authId",
      });
    }

    // Get contact information
    const { data: contact, error: contactError } = await supabase
      .from("contacts")
      .select("*")
      .eq("id", contactId)
      .eq("auth_id", authId)
      .single();

    if (contactError || !contact) {
      return res.status(404).json({
        error: "Contact not found",
      });
    }

    // Perform AI analysis
    const analysis = await analyzeContactConversation(
      authId,
      contact.phone_number,
      contactId,
      triggeredBy
    );

    if (!analysis) {
      return res.status(500).json({
        error: "Failed to analyze contact conversation",
      });
    }

    // Schedule follow-up if appropriate
    let followUpResult = null;
    if (contact.follow_up_enabled) {
      followUpResult = await scheduleFollowUpFromAnalysis(
        contactId,
        authId,
        contact.phone_number,
        analysis
      );
    }

    res.json({
      success: true,
      message: "Contact analysis completed",
      data: {
        analysis,
        followUp: followUpResult,
      },
    });

  } catch (error) {
    console.error("Error analyzing contact:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Get AI analysis history for a contact
router.get("/contacts/:contactId/analysis-history", async (req, res) => {
  try {
    const { contactId } = req.params;
    const { authId, limit = 10 } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    const { data: analysisHistory, error } = await supabase
      .from("contact_ai_analysis")
      .select("*")
      .eq("contact_id", contactId)
      .eq("auth_id", authId)
      .order("created_at", { ascending: false })
      .limit(parseInt(limit));

    if (error) throw error;

    res.json({
      success: true,
      data: analysisHistory,
    });

  } catch (error) {
    console.error("Error fetching analysis history:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Toggle follow-up for a contact
router.post("/contacts/:contactId/toggle-follow-up", async (req, res) => {
  try {
    const { contactId } = req.params;
    const { authId, enabled } = req.body;

    if (!authId || enabled === undefined) {
      return res.status(400).json({
        error: "Missing required fields: authId, enabled",
      });
    }

    let result;
    if (enabled) {
      result = await enableFollowUpsForContact(contactId);
    } else {
      result = await stopFollowUpsForContact(contactId, 'manual_stop');
    }

    if (!result) {
      return res.status(500).json({
        error: "Failed to update follow-up status",
      });
    }

    // Update contact in database
    const { data: updatedContact, error: updateError } = await supabase
      .from("contacts")
      .update({
        follow_up_enabled: enabled,
        follow_up_stopped_reason: enabled ? null : 'manual_stop',
        updated_at: new Date().toISOString(),
      })
      .eq("id", contactId)
      .eq("auth_id", authId)
      .select()
      .single();

    if (updateError) throw updateError;

    res.json({
      success: true,
      message: `Follow-ups ${enabled ? 'enabled' : 'disabled'} for contact`,
      data: updatedContact,
    });

  } catch (error) {
    console.error("Error toggling follow-up:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Get follow-up schedules for a contact
router.get("/contacts/:contactId/follow-ups", async (req, res) => {
  try {
    const { contactId } = req.params;
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    const { data: followUps, error } = await supabase
      .from("follow_up_schedules")
      .select("*")
      .eq("contact_id", contactId)
      .eq("auth_id", authId)
      .order("created_at", { ascending: false });

    if (error) throw error;

    res.json({
      success: true,
      data: followUps,
    });

  } catch (error) {
    console.error("Error fetching follow-ups:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Get follow-up rules for a business
router.get("/follow-up-rules", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    const { data: rules, error } = await supabase
      .from("follow_up_rules")
      .select("*")
      .eq("auth_id", authId)
      .order("created_at", { ascending: false });

    if (error) throw error;

    res.json({
      success: true,
      data: rules,
    });

  } catch (error) {
    console.error("Error fetching follow-up rules:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Create or update follow-up rule
router.post("/follow-up-rules", async (req, res) => {
  try {
    const {
      id,
      authId,
      ruleName,
      intentCategory,
      isActive = true,
      delayHours = 24,
      maxFollowUps = 1,
      messageTemplate,
      conditions = {}
    } = req.body;

    if (!authId || !ruleName || !intentCategory || !messageTemplate) {
      return res.status(400).json({
        error: "Missing required fields: authId, ruleName, intentCategory, messageTemplate",
      });
    }

    // Validate intent category
    if (!Object.values(INTENT_CATEGORIES).includes(intentCategory)) {
      return res.status(400).json({
        error: "Invalid intent category",
        validCategories: Object.values(INTENT_CATEGORIES),
      });
    }

    // Prepare the rule data
    const ruleData = {
      auth_id: authId,
      rule_name: ruleName,
      intent_category: intentCategory,
      is_active: isActive,
      delay_hours: delayHours,
      max_follow_ups: maxFollowUps,
      message_template: messageTemplate,
      conditions: conditions,
      updated_at: new Date().toISOString(),
    };

    // Include ID if updating existing rule
    if (id) {
      ruleData.id = id;

      // Verify the rule belongs to this user before updating
      const { data: existingRule, error: checkError } = await supabase
        .from("follow_up_rules")
        .select("auth_id")
        .eq("id", id)
        .single();

      if (checkError || !existingRule || existingRule.auth_id !== authId) {
        return res.status(403).json({
          error: "Rule not found or access denied",
        });
      }
    }

    const { data: rule, error } = await supabase
      .from("follow_up_rules")
      .upsert(ruleData)
      .select()
      .single();

    if (error) throw error;

    res.json({
      success: true,
      message: "Follow-up rule saved successfully",
      data: rule,
    });

  } catch (error) {
    console.error("Error saving follow-up rule:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Delete follow-up rule
router.delete("/follow-up-rules/:ruleId", async (req, res) => {
  try {
    const { ruleId } = req.params;
    const { authId } = req.body;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required field: authId",
      });
    }

    const { error } = await supabase
      .from("follow_up_rules")
      .delete()
      .eq("id", ruleId)
      .eq("auth_id", authId);

    if (error) throw error;

    res.json({
      success: true,
      message: "Follow-up rule deleted successfully",
    });

  } catch (error) {
    console.error("Error deleting follow-up rule:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Manually trigger follow-up processing
router.post("/follow-ups/process", async (req, res) => {
  try {
    const result = await triggerFollowUpProcessing();

    res.json({
      success: true,
      message: "Follow-up processing triggered",
      data: result,
    });

  } catch (error) {
    console.error("Error triggering follow-up processing:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Get available intent categories and auto-tags
router.get("/tagging/categories", async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        intentCategories: INTENT_CATEGORIES,
        autoTags: AUTO_TAGS,
      },
    });
  } catch (error) {
    console.error("Error fetching tagging categories:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Bulk analyze contacts
router.post("/contacts/bulk-analyze", async (req, res) => {
  try {
    const { authId, contactIds, triggeredBy = 'bulk_analysis' } = req.body;

    if (!authId || !contactIds || !Array.isArray(contactIds)) {
      return res.status(400).json({
        error: "Missing required fields: authId, contactIds (array)",
      });
    }

    const results = [];
    const errors = [];

    for (const contactId of contactIds) {
      try {
        // Get contact information
        const { data: contact, error: contactError } = await supabase
          .from("contacts")
          .select("phone_number")
          .eq("id", contactId)
          .eq("auth_id", authId)
          .single();

        if (contactError || !contact) {
          errors.push({ contactId, error: "Contact not found" });
          continue;
        }

        // Perform AI analysis
        const analysis = await analyzeContactConversation(
          authId,
          contact.phone_number,
          contactId,
          triggeredBy
        );

        if (analysis) {
          results.push({ contactId, analysis });
        } else {
          errors.push({ contactId, error: "Analysis failed" });
        }

      } catch (error) {
        errors.push({ contactId, error: error.message });
      }
    }

    res.json({
      success: true,
      message: `Bulk analysis completed. ${results.length} successful, ${errors.length} failed.`,
      data: {
        successful: results,
        failed: errors,
        totalProcessed: contactIds.length,
      },
    });

  } catch (error) {
    console.error("Error in bulk contact analysis:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Get recent follow-ups for a user
router.get("/contacts/recent-follow-ups", async (req, res) => {
  try {
    const { authId, limit = 20 } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    const { data: followUps, error } = await supabase
      .from("follow_up_schedules")
      .select(`
        *,
        contacts!inner(name, phone_number)
      `)
      .eq("auth_id", authId)
      .order("created_at", { ascending: false })
      .limit(parseInt(limit));

    if (error) throw error;

    // Format the data
    const formattedFollowUps = followUps.map(followUp => ({
      ...followUp,
      contact_name: followUp.contacts?.name,
      phone_number: followUp.contacts?.phone_number
    }));

    res.json({
      success: true,
      data: formattedFollowUps,
    });

  } catch (error) {
    console.error("Error fetching recent follow-ups:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Get follow-up analytics for a user
router.get("/follow-ups/user-analytics", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    const { data: followUps, error } = await supabase
      .from("follow_up_schedules")
      .select("status")
      .eq("auth_id", authId);

    if (error) throw error;

    // Calculate analytics
    const totalScheduled = followUps.length;
    const executed = followUps.filter(f => f.status === 'executed').length;
    const pending = followUps.filter(f => f.status === 'pending').length;
    const cancelled = followUps.filter(f => f.status === 'cancelled').length;
    const failed = followUps.filter(f => f.status === 'failed').length;

    const executionRate = totalScheduled > 0 ? Math.round((executed / totalScheduled) * 100) : 0;

    res.json({
      success: true,
      data: {
        totalScheduled,
        executed,
        pending,
        cancelled,
        failed,
        executionRate,
      },
    });

  } catch (error) {
    console.error("Error fetching follow-up analytics:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// Update contact auto tags (user-editable AI tags)
router.post("/contacts/:contactId/update-auto-tags", async (req, res) => {
  try {
    const { contactId } = req.params;
    const { authId, autoTags } = req.body;

    if (!authId || !Array.isArray(autoTags)) {
      return res.status(400).json({
        error: "Missing required fields: authId, autoTags (array)",
      });
    }

    // Verify contact belongs to this user
    const { data: contact, error: contactError } = await supabase
      .from("contacts")
      .select("id, phone_number")
      .eq("id", contactId)
      .eq("auth_id", authId)
      .single();

    if (contactError || !contact) {
      return res.status(404).json({
        error: "Contact not found",
      });
    }

    // Update the contact's auto_tags
    const { data: updatedContact, error: updateError } = await supabase
      .from("contacts")
      .update({
        auto_tags: autoTags,
        updated_at: new Date().toISOString()
      })
      .eq("id", contactId)
      .eq("auth_id", authId)
      .select()
      .single();

    if (updateError) throw updateError;

    // Log this as a manual AI analysis update
    const { error: analysisError } = await supabase
      .from("contact_ai_analysis")
      .insert({
        contact_id: contactId,
        auth_id: authId,
        phone_number: contact.phone_number,
        analysis_type: 'auto_tag',
        previous_value: contact.auto_tags ? contact.auto_tags.join(', ') : '',
        new_value: autoTags.join(', '),
        confidence_score: 1.0, // Manual edit = 100% confidence
        conversation_context: 'Manual tag edit by user',
        ai_reasoning: 'User manually updated AI tags',
        triggered_by: 'manual_edit'
      });

    if (analysisError) {
      console.error("Error logging manual tag update:", analysisError);
      // Don't fail the request if logging fails
    }

    res.json({
      success: true,
      message: "Auto tags updated successfully",
      data: updatedContact,
    });

  } catch (error) {
    console.error("Error updating auto tags:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});



// ===== MESSENGER ROUTES =====

// Messenger webhook verification (same endpoint as WhatsApp but handles both)
router.get("/messenger/webhook", (req, res) => {
  const mode = req.query["hub.mode"];
  const token = req.query["hub.verify_token"];
  const challenge = req.query["hub.challenge"];

  // Check if a token and mode were sent
  if (mode && token) {
    // Check the mode and token sent are correct
    if (mode === "subscribe" && token === process.env.VERIFY_TOKEN) {
      // Respond with 200 OK and challenge token from the request
      res.status(200).send(challenge);
    } else {
      // Response with '403 Forbidden' if tokens don't match
      res.sendStatus(403);
    }
  }
});

/**
 * Messenger Webhook Handler
 *
 * Supports multiple message types:
 * - Text messages: Processed directly
 * - Images: Analyzed using OpenAI GPT-4 Vision API
 * - Audio: Transcribed using OpenAI Whisper API
 * - Other types: Ignored (files, stickers, etc.)
 */
router.post("/messenger/webhook", async (req, res) => {
  try {
    const body = req.body;

    // Check if this is a Messenger page notification
    if (body.object === "page") {
      if (body.entry && body.entry[0].messaging && body.entry[0].messaging[0]) {
        const messaging = body.entry[0].messaging[0];
        const senderId = messaging.sender.id;
        const pageId = messaging.recipient.id;
        const message = messaging.message;

        if (message) {
          logger.info(`Messenger message received | From: ${senderId} | Page: ${pageId}`);

          // Process the message content
          const { content: messageContent, type: messageType } = processMessengerMessage(message);

          logger.info(`Messenger message processed | Type: ${messageType} | Content: ${messageContent.substring(0, 100)}...`);

          // Find the customer associated with this page
          const customer = await findCustomerByPlatformId("messenger", pageId);

          if (customer) {
            logger.info(`Processing Messenger message | From: ${senderId} | Customer: ${customer.auth_id} | Type: ${messageType}`);

            // Process the message through our AI system
            await processIncomingMessage(
              customer,
              senderId,
              messageContent,
              pageId,
              messageType,
              "messenger"
            );
          } else {
            logger.warn(`No customer configuration found for Messenger page: ${pageId}`);
          }
        }
      }

      res.status(200).send("EVENT_RECEIVED");
    } else {
      res.sendStatus(404);
    }
  } catch (error) {
    console.error("Messenger webhook error:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Register Messenger integration
router.post("/messenger/register", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const {
      messengerPageId,
      messengerAccessToken,
      messengerAppId,
    } = req.body;

    if (!messengerPageId || !messengerAccessToken) {
      return res.status(400).json({
        error: "Messenger page ID and access token are required"
      });
    }

    // Validate Messenger credentials
    const validation = await validateMessengerCredentials(messengerAccessToken, messengerPageId);
    if (!validation.isValid) {
      return res.status(400).json({
        error: "Invalid Messenger credentials or access token",
        details: validation.error
      });
    }

    // Save Messenger integration
    const integration = await saveSocialMediaIntegration(authId, "messenger", {
      messengerPageId,
      messengerAccessToken,
      messengerAppId,
      isEnabled: true,
    });

    // Provide webhook setup information
    const webhookUrl = `${req.protocol}://${req.get("host")}/api/ai/messenger/webhook`;
    const verifyToken = process.env.VERIFY_TOKEN;

    res.json({
      success: true,
      data: {
        integration,
        webhookUrl,
        verifyToken,
        pageId: messengerPageId,
        instructions: {
          step1: "Go to your Meta for Developers dashboard",
          step2: "Select your Messenger App",
          step3: "Go to Messenger > Settings",
          step4: `Set Webhook URL to: ${webhookUrl}`,
          step5: `Set Verify Token to: ${verifyToken}`,
          step6: "Subscribe to 'messages' webhook field",
          step7: "Save your configuration",
        },
      },
    });
  } catch (error) {
    console.error("Error registering Messenger integration:", error);
    res.status(500).json({ error: "Failed to register Messenger integration" });
  }
});

// Get Messenger integration status
router.get("/messenger/status", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const integration = await getPlatformIntegration(authId, "messenger");

    if (!integration) {
      return res.json({
        success: true,
        data: null,
        notFound: true,
      });
    }

    // Remove sensitive data before sending
    const safeIntegration = {
      ...integration,
      messenger_access_token: integration.messenger_access_token ? "***" : null,
    };

    res.json({
      success: true,
      data: safeIntegration,
    });
  } catch (error) {
    console.error("Error fetching Messenger integration status:", error);
    res.status(500).json({ error: "Failed to fetch Messenger integration status" });
  }
});

// Toggle Messenger integration
router.post("/messenger/toggle", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { isEnabled } = req.body;

    const integration = await togglePlatformIntegration(authId, "messenger", isEnabled);

    res.json({
      success: true,
      data: integration,
    });
  } catch (error) {
    console.error("Error toggling Messenger integration:", error);
    res.status(500).json({ error: "Failed to toggle Messenger integration" });
  }
});

// ===== INSTAGRAM ROUTES =====

// Instagram webhook verification
router.get("/instagram/webhook", (req, res) => {
  const mode = req.query["hub.mode"];
  const token = req.query["hub.verify_token"];
  const challenge = req.query["hub.challenge"];

  // Check if a token and mode were sent
  if (mode && token) {
    // Check the mode and token sent are correct
    if (mode === "subscribe" && token === process.env.VERIFY_TOKEN) {
      // Respond with 200 OK and challenge token from the request
      res.status(200).send(challenge);
    } else {
      // Response with '403 Forbidden' if tokens don't match
      res.sendStatus(403);
    }
  }
});

/**
 * Instagram Webhook Handler
 *
 * Supports multiple message types:
 * - Text messages: Processed directly
 * - Images: Analyzed using OpenAI GPT-4 Vision API
 * - Story mentions: Processed as text
 * - Other types: Ignored
 */
router.post("/instagram/webhook", async (req, res) => {
  try {
    const body = req.body;

    // Check if this is an Instagram notification
    if (body.object === "instagram") {
      if (body.entry && body.entry[0].messaging && body.entry[0].messaging[0]) {
        const messaging = body.entry[0].messaging[0];
        const senderId = messaging.sender.id;
        const recipientId = messaging.recipient.id;
        const message = messaging.message;

        if (message) {
          logger.info(`Instagram message received | From: ${senderId} | To: ${recipientId}`);

          // Process the message content
          const { content: messageContent, type: messageType } = processInstagramMessage(message);

          logger.info(`Instagram message processed | Type: ${messageType} | Content: ${messageContent.substring(0, 100)}...`);

          // Find the customer associated with this Instagram account
          const customer = await findCustomerByPlatformId("instagram", recipientId);

          if (customer) {
            logger.info(`Processing Instagram message | From: ${senderId} | Customer: ${customer.auth_id} | Type: ${messageType}`);

            // Process the message through our AI system
            await processIncomingMessage(
              customer,
              senderId,
              messageContent,
              recipientId,
              messageType,
              "instagram"
            );
          } else {
            logger.warn(`No customer configuration found for Instagram account: ${recipientId}`);
          }
        }
      }

      res.status(200).send("EVENT_RECEIVED");
    } else {
      res.sendStatus(404);
    }
  } catch (error) {
    console.error("Instagram webhook error:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Register Instagram integration
router.post("/instagram/register", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const {
      instagramAccountId,
      instagramAccessToken,
    } = req.body;

    if (!instagramAccountId || !instagramAccessToken) {
      return res.status(400).json({
        error: "Instagram account ID and access token are required"
      });
    }

    // Validate Instagram credentials
    const validation = await validateInstagramCredentials(instagramAccessToken, instagramAccountId);
    if (!validation.isValid) {
      return res.status(400).json({
        error: "Invalid Instagram credentials or access token",
        details: validation.error
      });
    }

    // Save Instagram integration
    const integration = await saveSocialMediaIntegration(authId, "instagram", {
      instagramAccountId,
      instagramAccessToken,
      isEnabled: true,
    });

    // Provide webhook setup information
    const webhookUrl = `${req.protocol}://${req.get("host")}/api/ai/instagram/webhook`;
    const verifyToken = process.env.VERIFY_TOKEN;

    res.json({
      success: true,
      data: {
        integration,
        webhookUrl,
        verifyToken,
        accountId: instagramAccountId,
        instructions: {
          step1: "Go to your Meta for Developers dashboard",
          step2: "Select your Instagram App",
          step3: "Go to Instagram > Configuration",
          step4: `Set Webhook URL to: ${webhookUrl}`,
          step5: `Set Verify Token to: ${verifyToken}`,
          step6: "Subscribe to 'messages' webhook field",
          step7: "Save your configuration",
        },
      },
    });
  } catch (error) {
    console.error("Error registering Instagram integration:", error);
    res.status(500).json({ error: "Failed to register Instagram integration" });
  }
});

// Get Instagram integration status
router.get("/instagram/status", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const integration = await getPlatformIntegration(authId, "instagram");

    if (!integration) {
      return res.json({
        success: true,
        data: null,
        notFound: true,
      });
    }

    // Remove sensitive data before sending
    const safeIntegration = {
      ...integration,
      instagram_access_token: integration.instagram_access_token ? "***" : null,
    };

    res.json({
      success: true,
      data: safeIntegration,
    });
  } catch (error) {
    console.error("Error fetching Instagram integration status:", error);
    res.status(500).json({ error: "Failed to fetch Instagram integration status" });
  }
});

// Toggle Instagram integration
router.post("/instagram/toggle", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { isEnabled } = req.body;

    const integration = await togglePlatformIntegration(authId, "instagram", isEnabled);

    res.json({
      success: true,
      data: integration,
    });
  } catch (error) {
    console.error("Error toggling Instagram integration:", error);
    res.status(500).json({ error: "Failed to toggle Instagram integration" });
  }
});

// ===== GOOGLE INTEGRATIONS ROUTES =====

// Get all Google integrations for a user
router.get("/google/integrations", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const integrations = await getGoogleIntegrations(authId);

    res.json({
      success: true,
      data: integrations,
    });
  } catch (error) {
    console.error("Error fetching Google integrations:", error);
    res.status(500).json({ error: "Failed to fetch Google integrations" });
  }
});

// Get specific Google service integration
router.get("/google/:serviceType", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { serviceType } = req.params;

    if (!["sheets", "calendar"].includes(serviceType)) {
      return res.status(400).json({ error: "Invalid service type" });
    }

    const integration = await getGoogleServiceIntegration(authId, serviceType);

    if (!integration) {
      return res.json({
        success: true,
        data: null,
        notFound: true,
      });
    }

    res.json({
      success: true,
      data: integration,
    });
  } catch (error) {
    console.error(`Error fetching Google ${req.params.serviceType} integration:`, error);
    res.status(500).json({ error: `Failed to fetch Google ${req.params.serviceType} integration` });
  }
});

// Save Google Sheets integration
router.post("/google/sheets", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const {
      spreadsheetUrl,
      spreadsheetId: providedSpreadsheetId,
      worksheetName,
      spreadsheetColumns,
    } = req.body;

    let spreadsheetId = providedSpreadsheetId;

    // Extract spreadsheet ID from URL if provided
    if (spreadsheetUrl && !spreadsheetId) {
      spreadsheetId = extractSpreadsheetIdFromUrl(spreadsheetUrl);
      if (!spreadsheetId) {
        return res.status(400).json({
          error: "Invalid Google Sheets URL. Please provide a valid spreadsheet URL or ID."
        });
      }
    }

    if (!spreadsheetId) {
      return res.status(400).json({
        error: "Spreadsheet ID or URL is required"
      });
    }

    // Validate Google Sheets access
    const validation = await validateGoogleSheetsAccess(spreadsheetId, worksheetName || "Data");
    if (!validation.isValid) {
      const errorMessage = validation.availableWorksheets
        ? `${validation.error}. Available worksheets: ${validation.availableWorksheets.join(', ')}`
        : validation.error;

      return res.status(400).json({
        error: errorMessage,
        details: validation.error,
        availableWorksheets: validation.availableWorksheets,
        suggestedWorksheet: validation.suggestedWorksheet
      });
    }

    // Save Google Sheets integration
    const integration = await saveGoogleIntegration(authId, "sheets", {
      spreadsheetId,
      spreadsheetUrl: spreadsheetUrl || `https://docs.google.com/spreadsheets/d/${spreadsheetId}`,
      worksheetName: worksheetName || "Data",
      spreadsheetColumns: spreadsheetColumns || [
        'OrderID', 'Date','Time', 'Customer Phone', 'Customer Name', 'Address', 'Product', 'Variation', 'Quantity', 'Total Amount', 'Status'
      ],
      isEnabled: true,
    });

    res.json({
      success: true,
      data: {
        integration,
        spreadsheetInfo: validation.spreadsheetInfo,
      },
    });
  } catch (error) {
    console.error("Error saving Google Sheets integration:", error);
    res.status(500).json({ error: "Failed to save Google Sheets integration" });
  }
});

// Save Google Calendar integration (for future use)
router.post("/google/calendar", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const {
      calendarId,
      calendarName,
    } = req.body;

    if (!calendarId) {
      return res.status(400).json({
        error: "Calendar ID is required"
      });
    }

    // Save Google Calendar integration
    const integration = await saveGoogleIntegration(authId, "calendar", {
      calendarId,
      calendarName,
      isEnabled: true,
    });

    res.json({
      success: true,
      data: integration,
    });
  } catch (error) {
    console.error("Error saving Google Calendar integration:", error);
    res.status(500).json({ error: "Failed to save Google Calendar integration" });
  }
});

// Toggle Google service integration
router.post("/google/:serviceType/toggle", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { serviceType } = req.params;
    const { isEnabled } = req.body;

    if (!["sheets", "calendar"].includes(serviceType)) {
      return res.status(400).json({ error: "Invalid service type" });
    }

    const integration = await toggleGoogleIntegration(authId, serviceType, isEnabled);

    res.json({
      success: true,
      data: integration,
    });
  } catch (error) {
    console.error(`Error toggling Google ${req.params.serviceType} integration:`, error);
    res.status(500).json({ error: `Failed to toggle Google ${req.params.serviceType} integration` });
  }
});

// ===== SOCIAL MEDIA MANAGEMENT ROUTES =====

// Get all social media integrations for a user
router.get("/social-media/integrations", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    // Get OAuth integrations (Messenger and Instagram only)
    const integrations = await getSocialMediaIntegrations(authId);

    // Get WhatsApp status from the old system
    let whatsappStatus = {
      platform: "whatsapp",
      is_enabled: false,
      configured: false,
      oauth_connected: false
    };

    try {
      const { data: whatsappData, error: whatsappError } = await supabase
        .from("whatsapp_customers")
        .select("*")
        .eq("auth_id", authId)
        .single();

      if (!whatsappError && whatsappData) {
        whatsappStatus = {
          platform: "whatsapp",
          is_enabled: whatsappData.is_active || false,
          configured: !!(whatsappData.whatsapp_business_account_id && whatsappData.whatsapp_phone_number_id),
          oauth_connected: false, // WhatsApp doesn't use OAuth
          whatsapp_business_account_id: whatsappData.whatsapp_business_account_id,
          whatsapp_phone_number_id: whatsappData.whatsapp_phone_number_id
        };
      }
    } catch (error) {
      console.log("WhatsApp data not found, using defaults");
    }

    // Create a complete status object for all platforms
    const platformStatus = {
      whatsapp: whatsappStatus,
      messenger: integrations.find(i => i.platform === "messenger") || {
        platform: "messenger",
        is_enabled: false,
        configured: false,
        oauth_connected: false
      },
      instagram: integrations.find(i => i.platform === "instagram") || {
        platform: "instagram",
        is_enabled: false,
        configured: false,
        oauth_connected: false
      },
    };

    // Mark OAuth platforms as configured if they have required fields
    platformStatus.messenger.configured = !!(platformStatus.messenger.oauth_connected && platformStatus.messenger.messenger_page_id);
    platformStatus.instagram.configured = !!(platformStatus.instagram.oauth_connected && platformStatus.instagram.instagram_account_id);

    res.json({
      success: true,
      data: {
        integrations,
        platformStatus,
      },
    });
  } catch (error) {
    console.error("Error fetching social media integrations:", error);
    res.status(500).json({ error: "Failed to fetch social media integrations" });
  }
});

// Get enabled platforms for a user
router.get("/social-media/enabled", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const enabledPlatforms = await getEnabledPlatforms(authId);

    res.json({
      success: true,
      data: enabledPlatforms,
    });
  } catch (error) {
    console.error("Error fetching enabled platforms:", error);
    res.status(500).json({ error: "Failed to fetch enabled platforms" });
  }
});

// Bulk toggle multiple platforms
router.post("/social-media/bulk-toggle", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { platforms } = req.body;

    if (!platforms || typeof platforms !== "object") {
      return res.status(400).json({ error: "Platforms configuration is required" });
    }

    const results = {};
    const errors = {};

    // Process each platform toggle
    for (const [platform, isEnabled] of Object.entries(platforms)) {
      if (!["whatsapp", "messenger", "instagram"].includes(platform)) {
        errors[platform] = "Invalid platform";
        continue;
      }

      try {
        const integration = await togglePlatformIntegration(authId, platform, isEnabled);
        results[platform] = integration;
      } catch (error) {
        errors[platform] = error.message;
      }
    }

    res.json({
      success: Object.keys(errors).length === 0,
      data: results,
      errors: Object.keys(errors).length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error("Error bulk toggling platforms:", error);
    res.status(500).json({ error: "Failed to toggle platforms" });
  }
});

// Get platform configuration status
router.get("/social-media/status", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const integrations = await getSocialMediaIntegrations(authId);

    const status = {
      totalPlatforms: 3,
      configuredPlatforms: 0,
      enabledPlatforms: 0,
      platforms: {
        whatsapp: { configured: false, enabled: false },
        messenger: { configured: false, enabled: false },
        instagram: { configured: false, enabled: false },
      },
    };

    integrations.forEach(integration => {
      const platform = integration.platform;
      if (status.platforms[platform]) {
        status.platforms[platform].enabled = integration.is_enabled;

        // Check if platform is properly configured
        switch (platform) {
          case "whatsapp":
            status.platforms[platform].configured = !!(
              integration.whatsapp_business_account_id &&
              integration.whatsapp_phone_number_id
            );
            break;
          case "messenger":
            status.platforms[platform].configured = !!(
              integration.messenger_page_id &&
              integration.messenger_access_token
            );
            break;
          case "instagram":
            status.platforms[platform].configured = !!(
              integration.instagram_account_id &&
              integration.instagram_access_token
            );
            break;
        }

        if (status.platforms[platform].configured) {
          status.configuredPlatforms++;
        }
        if (status.platforms[platform].enabled) {
          status.enabledPlatforms++;
        }
      }
    });

    res.json({
      success: true,
      data: status,
    });
  } catch (error) {
    console.error("Error fetching platform status:", error);
    res.status(500).json({ error: "Failed to fetch platform status" });
  }
});

// ===== MIGRATION ROUTES =====

// Migrate existing WhatsApp customers to new social media integrations table
router.post("/migrate/whatsapp-to-social-media", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    // Check if user is admin (optional security check)
    // You might want to add admin verification here

    const result = await migrateWhatsAppCustomers();

    res.json({
      success: true,
      data: result,
      message: `Successfully migrated ${result.migrated} WhatsApp customers`,
    });
  } catch (error) {
    console.error("Error running WhatsApp migration:", error);
    res.status(500).json({ error: "Failed to run migration" });
  }
});

// ===== WHATSAPP STATUS ROUTE (separate from OAuth) =====

// Get WhatsApp integration status (using old system)
router.get("/whatsapp/status", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { data: whatsappData, error } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .single();

    if (error && error.code !== "PGRST116") {
      console.error("Error fetching WhatsApp status:", error);
      return res.status(500).json({ error: "Failed to fetch WhatsApp status" });
    }

    res.json({
      success: true,
      data: whatsappData || null,
      notFound: !whatsappData,
    });
  } catch (error) {
    console.error("Error fetching WhatsApp status:", error);
    res.status(500).json({ error: "Failed to fetch WhatsApp status" });
  }
});

// ===== META OAUTH ROUTES =====

// Generate OAuth URL for platform connection
router.post("/oauth/:platform/authorize", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { platform } = req.params;
    const { redirectUri } = req.body;

    if (!["messenger", "instagram"].includes(platform)) {
      return res.status(400).json({ error: "Invalid platform. WhatsApp uses system tokens, not OAuth." });
    }

    if (!redirectUri) {
      return res.status(400).json({ error: "Redirect URI is required" });
    }

    const result = await generateOAuthURL(authId, platform, redirectUri);

    if (result.success) {
      res.json({
        success: true,
        data: {
          authUrl: result.url,
          state: result.state,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Error generating OAuth URL:", error);
    res.status(500).json({ error: "Failed to generate OAuth URL" });
  }
});

// Handle OAuth callback and exchange code for token
router.post("/oauth/:platform/callback", async (req, res) => {
  try {
    const { platform } = req.params;
    const { code, state, redirectUri } = req.body;

    if (!["messenger", "instagram"].includes(platform)) {
      return res.status(400).json({ error: "Invalid platform. WhatsApp uses system tokens, not OAuth." });
    }

    if (!code || !state) {
      return res.status(400).json({ error: "Authorization code and state are required" });
    }

    // Exchange code for token
    const tokenResult = await exchangeCodeForToken(code, state, redirectUri);

    if (!tokenResult.success) {
      return res.status(400).json({
        success: false,
        error: tokenResult.error,
      });
    }

    const { tokenData, stateData } = tokenResult;

    // Get long-lived token
    const longLivedResult = await getLongLivedToken(tokenData.access_token);
    const finalToken = longLivedResult.success ? longLivedResult.access_token : tokenData.access_token;
    const finalExpiresAt = longLivedResult.success ? longLivedResult.expires_at : tokenData.expires_at;

    // Get platform-specific account information
    let accountData = {};

    if (platform === "messenger") {
      const pagesResult = await getUserPages(finalToken);
      if (pagesResult.success) {
        accountData.pages = pagesResult.pages;
      }
    } else if (platform === "instagram") {
      const igResult = await getUserInstagramAccounts(finalToken);
      if (igResult.success) {
        accountData.accounts = igResult.accounts;
      }
    } else if (platform === "whatsapp") {
      const waResult = await getUserWhatsAppAccounts(finalToken);
      if (waResult.success) {
        accountData.accounts = waResult.accounts;
      }
    }

    // Store OAuth connection in database
    const integrationData = {
      auth_id: stateData.auth_id,
      platform: platform,
      oauth_connected: true,
      access_token: finalToken,
      token_expires_at: finalExpiresAt,
      token_type: tokenData.token_type,
      meta_user_id: tokenData.user_id,
      meta_user_name: tokenData.user_name,
      connection_status: 'connected',
      last_sync_at: new Date().toISOString(),
      is_enabled: true,
    };

    const { data: integration, error: integrationError } = await supabase
      .from("social_media_integrations")
      .upsert(integrationData, {
        onConflict: 'auth_id,platform'
      })
      .select()
      .single();

    if (integrationError) {
      logger.error("Error storing OAuth integration:", integrationError);
      // Provide more specific error message for duplicate key constraint
      if (integrationError.code === '23505') {
        return res.status(409).json({
          error: "Integration already exists. Please disconnect first before reconnecting.",
          code: "DUPLICATE_INTEGRATION"
        });
      }
      return res.status(500).json({ error: "Failed to store integration" });
    }

    logger.info(`Successfully connected ${platform} OAuth integration for user ${stateData.auth_id}`, {
      platform,
      userId: stateData.auth_id,
      integrationId: integration.id,
      connectionStatus: integration.connection_status
    });

    res.json({
      success: true,
      data: {
        integration,
        accountData,
        user: {
          id: tokenData.user_id,
          name: tokenData.user_name,
          email: tokenData.user_email,
        },
      },
    });
  } catch (error) {
    console.error("Error handling OAuth callback:", error);
    res.status(500).json({ error: "Failed to process OAuth callback" });
  }
});

// Complete platform setup after OAuth
router.post("/oauth/:platform/setup", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { platform } = req.params;
    const setupData = req.body;

    if (!["whatsapp", "messenger", "instagram"].includes(platform)) {
      return res.status(400).json({ error: "Invalid platform" });
    }

    // Get existing OAuth integration
    const { data: integration, error: integrationError } = await supabase
      .from("social_media_integrations")
      .select("*")
      .eq("auth_id", authId)
      .eq("platform", platform)
      .eq("oauth_connected", true)
      .single();

    if (integrationError || !integration) {
      return res.status(404).json({ error: "OAuth integration not found. Please connect first." });
    }

    // Update integration with platform-specific setup data
    const updateData = {
      updated_at: new Date().toISOString(),
    };

    if (platform === "messenger" && setupData.pageId) {
      updateData.messenger_page_id = setupData.pageId;
      updateData.messenger_page_name = setupData.pageName;
    } else if (platform === "instagram" && setupData.accountId) {
      updateData.instagram_account_id = setupData.accountId;
      updateData.instagram_username = setupData.username;
    } else if (platform === "whatsapp" && setupData.businessAccountId && setupData.phoneNumberId) {
      updateData.whatsapp_business_account_id = setupData.businessAccountId;
      updateData.whatsapp_phone_number_id = setupData.phoneNumberId;
    }

    const { data: updatedIntegration, error: updateError } = await supabase
      .from("social_media_integrations")
      .update(updateData)
      .eq("id", integration.id)
      .select()
      .single();

    if (updateError) {
      logger.error("Error updating integration setup:", updateError);
      return res.status(500).json({ error: "Failed to complete setup" });
    }

    res.json({
      success: true,
      data: updatedIntegration,
    });
  } catch (error) {
    console.error("Error completing platform setup:", error);
    res.status(500).json({ error: "Failed to complete platform setup" });
  }
});

// Disconnect OAuth integration
router.post("/oauth/:platform/disconnect", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { platform } = req.params;

    if (!["whatsapp", "messenger", "instagram"].includes(platform)) {
      return res.status(400).json({ error: "Invalid platform" });
    }

    // Update integration to disconnect OAuth and clear all OAuth-related fields
    const { data: integration, error: updateError } = await supabase
      .from("social_media_integrations")
      .update({
        oauth_connected: false,
        access_token: null,
        refresh_token: null,
        token_expires_at: null,
        token_type: null,
        granted_scopes: null,
        meta_user_id: null,
        meta_user_name: null,
        connection_status: 'disconnected',
        is_enabled: false,
        last_error_message: null,
        updated_at: new Date().toISOString(),
      })
      .eq("auth_id", authId)
      .eq("platform", platform)
      .select()
      .single();

    if (updateError) {
      logger.error("Error disconnecting OAuth integration:", updateError);
      return res.status(500).json({ error: "Failed to disconnect integration" });
    }

    if (!integration) {
      logger.warn(`No ${platform} integration found for user ${authId} to disconnect`);
      return res.status(404).json({ error: "Integration not found" });
    }

    logger.info(`Successfully disconnected ${platform} OAuth integration for user ${authId}`);

    res.json({
      success: true,
      data: integration,
    });
  } catch (error) {
    console.error("Error disconnecting OAuth integration:", error);
    res.status(500).json({ error: "Failed to disconnect integration" });
  }
});

// ===== TOKEN REFRESH ROUTES =====

// Refresh tokens for all users (admin/cron endpoint)
router.post("/tokens/refresh-all", async (req, res) => {
  try {
    // This endpoint should be protected in production (admin only or API key)
    const result = await refreshExpiredTokens();

    res.json({
      success: result.success,
      data: result.success ? {
        refreshed: result.refreshed,
        errors: result.errors,
        total: result.total,
      } : null,
      error: result.success ? null : result.error,
    });
  } catch (error) {
    console.error("Error refreshing all tokens:", error);
    res.status(500).json({ error: "Failed to refresh tokens" });
  }
});

// Refresh token for specific user and platform
router.post("/tokens/refresh/:platform", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { platform } = req.params;

    if (!["whatsapp", "messenger", "instagram"].includes(platform)) {
      return res.status(400).json({ error: "Invalid platform" });
    }

    const result = await refreshUserToken(authId, platform);

    if (result.success) {
      res.json({
        success: true,
        message: `${platform.charAt(0).toUpperCase() + platform.slice(1)} token refreshed successfully`,
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Error refreshing user token:", error);
    res.status(500).json({ error: "Failed to refresh token" });
  }
});

// Get token status for user and platform
router.get("/tokens/status/:platform", async (req, res) => {
  try {
    const authId = req.headers["x-auth-id"];
    if (!authId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { platform } = req.params;

    if (!["whatsapp", "messenger", "instagram"].includes(platform)) {
      return res.status(400).json({ error: "Invalid platform" });
    }

    const status = await getTokenStatus(authId, platform);

    res.json({
      success: true,
      data: status,
    });
  } catch (error) {
    console.error("Error getting token status:", error);
    res.status(500).json({ error: "Failed to get token status" });
  }
});

// Cleanup expired OAuth states (maintenance endpoint)
router.post("/oauth/cleanup", async (req, res) => {
  try {
    const result = await cleanupExpiredOAuthStates();

    res.json({
      success: result.success,
      message: result.success ? "OAuth states cleaned up successfully" : result.error,
    });
  } catch (error) {
    console.error("Error cleaning up OAuth states:", error);
    res.status(500).json({ error: "Failed to cleanup OAuth states" });
  }
});

// Enhanced Follow-up Analytics Endpoints

// Get comprehensive follow-up analytics
router.get("/follow-up-analytics", async (req, res) => {
  try {
    const { authId, timeRange = '30d', includeOptimizations = 'true' } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    const analytics = await getFollowUpAnalytics(authId, {
      timeRange,
      includeOptimizations: includeOptimizations === 'true'
    });

    if (!analytics.success) {
      return res.status(500).json({
        error: analytics.error || "Failed to generate follow-up analytics"
      });
    }

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error("Error getting follow-up analytics:", error);
    res.status(500).json({ error: "Failed to get follow-up analytics" });
  }
});

// Get follow-up dashboard data
router.get("/follow-up-dashboard", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required parameter: authId",
      });
    }

    const dashboard = await getFollowUpDashboard(authId);

    if (!dashboard.success) {
      return res.status(500).json({
        error: dashboard.error || "Failed to get follow-up dashboard"
      });
    }

    res.json({
      success: true,
      data: dashboard
    });

  } catch (error) {
    console.error("Error getting follow-up dashboard:", error);
    res.status(500).json({ error: "Failed to get follow-up dashboard" });
  }
});

// Track follow-up outcome
router.post("/track-follow-up-outcome", async (req, res) => {
  try {
    const { followUpId, authId, outcome, metadata } = req.body;

    if (!followUpId || !authId || !outcome) {
      return res.status(400).json({
        error: "Missing required fields: followUpId, authId, outcome",
      });
    }

    await trackFollowUpOutcome(followUpId, authId, outcome, metadata);

    res.json({
      success: true,
      message: "Follow-up outcome tracked successfully"
    });

  } catch (error) {
    console.error("Error tracking follow-up outcome:", error);
    res.status(500).json({ error: "Failed to track follow-up outcome" });
  }
});



export default router;