import supabase from "./supabase.js";
import logger from "./logger.js";
import { INTENT_CATEGORIES, URGENCY_LEVELS } from "./aiTagging.js";


/**
 * Smart Follow-up Analytics System
 * Provides comprehensive analytics and optimization insights for follow-up performance
 */

/**
 * Get comprehensive follow-up analytics for a business
 * @param {string} authId - Business owner's auth ID
 * @param {Object} options - Analytics options (timeRange, includeOptimizations)
 * @returns {Object} Comprehensive analytics data
 */
export async function getFollowUpAnalytics(authId, options = {}) {
  try {
    const { timeRange = '30d', includeOptimizations = true } = options;
    
    logger.info(`📊 Generating follow-up analytics for ${authId} | Time range: ${timeRange}`);

    // Get date range for analysis
    const dateRange = getDateRange(timeRange);
    
    // Parallel fetch of all analytics data
    const [
      overviewMetrics,
      performanceByIntent,
      performanceByUrgency,
      templatePerformance,
      timingAnalysis,
      responsePatterns,
      conversionMetrics
    ] = await Promise.all([
      getOverviewMetrics(authId, dateRange),
      getPerformanceByIntent(authId, dateRange),
      getPerformanceByUrgency(authId, dateRange),
      getTemplatePerformance(authId, dateRange),
      getTimingAnalysis(authId, dateRange),
      getResponsePatterns(authId, dateRange),
      getConversionMetrics(authId, dateRange)
    ]);

    // Generate optimization recommendations
    const optimizations = includeOptimizations 
      ? await generateOptimizationRecommendations(authId, {
          overviewMetrics,
          performanceByIntent,
          templatePerformance,
          timingAnalysis
        })
      : null;

    return {
      success: true,
      timeRange,
      generatedAt: new Date().toISOString(),
      overview: overviewMetrics,
      performance: {
        byIntent: performanceByIntent,
        byUrgency: performanceByUrgency,
        byTemplate: templatePerformance
      },
      timing: timingAnalysis,
      responses: responsePatterns,
      conversions: conversionMetrics,
      optimizations
    };

  } catch (error) {
    logger.error("Error generating follow-up analytics:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Get overview metrics for follow-ups
 * @param {string} authId - Business owner's auth ID
 * @param {Object} dateRange - Date range object
 * @returns {Object} Overview metrics
 */
async function getOverviewMetrics(authId, dateRange) {
  const { data: metrics } = await supabase.rpc("get_follow_up_overview_metrics", {
    p_auth_id: authId,
    p_start_date: dateRange.start,
    p_end_date: dateRange.end
  });

  return metrics || {
    totalScheduled: 0,
    totalSent: 0,
    totalResponded: 0,
    totalConverted: 0,
    responseRate: 0,
    conversionRate: 0,
    avgResponseTime: 0
  };
}

/**
 * Get performance metrics by intent category
 * @param {string} authId - Business owner's auth ID
 * @param {Object} dateRange - Date range object
 * @returns {Array} Performance by intent
 */
async function getPerformanceByIntent(authId, dateRange) {
  const { data: performance } = await supabase.rpc("get_follow_up_performance_by_intent", {
    p_auth_id: authId,
    p_start_date: dateRange.start,
    p_end_date: dateRange.end
  });

  return performance || [];
}

/**
 * Get performance metrics by urgency level
 * @param {string} authId - Business owner's auth ID
 * @param {Object} dateRange - Date range object
 * @returns {Array} Performance by urgency
 */
async function getPerformanceByUrgency(authId, dateRange) {
  const { data: performance } = await supabase.rpc("get_follow_up_performance_by_urgency", {
    p_auth_id: authId,
    p_start_date: dateRange.start,
    p_end_date: dateRange.end
  });

  return performance || [];
}

/**
 * Get template performance metrics
 * @param {string} authId - Business owner's auth ID
 * @param {Object} dateRange - Date range object
 * @returns {Array} Template performance
 */
async function getTemplatePerformance(authId, dateRange) {
  const { data: performance } = await supabase.rpc("get_template_performance_metrics", {
    p_auth_id: authId,
    p_start_date: dateRange.start,
    p_end_date: dateRange.end
  });

  return performance || [];
}

/**
 * Get timing analysis for optimal follow-up scheduling
 * @param {string} authId - Business owner's auth ID
 * @param {Object} dateRange - Date range object
 * @returns {Object} Timing analysis
 */
async function getTimingAnalysis(authId, dateRange) {
  const { data: timing } = await supabase.rpc("get_follow_up_timing_analysis", {
    p_auth_id: authId,
    p_start_date: dateRange.start,
    p_end_date: dateRange.end
  });

  return timing || {
    optimalHours: [],
    optimalDays: [],
    avgDelayToResponse: 0,
    bestPerformingDelays: []
  };
}

/**
 * Get customer response patterns
 * @param {string} authId - Business owner's auth ID
 * @param {Object} dateRange - Date range object
 * @returns {Object} Response patterns
 */
async function getResponsePatterns(authId, dateRange) {
  const { data: patterns } = await supabase.rpc("get_customer_response_patterns", {
    p_auth_id: authId,
    p_start_date: dateRange.start,
    p_end_date: dateRange.end
  });

  return patterns || {
    responseTimeDistribution: [],
    commonResponseTypes: [],
    engagementByTimeOfDay: []
  };
}

/**
 * Get conversion metrics
 * @param {string} authId - Business owner's auth ID
 * @param {Object} dateRange - Date range object
 * @returns {Object} Conversion metrics
 */
async function getConversionMetrics(authId, dateRange) {
  const { data: conversions } = await supabase.rpc("get_follow_up_conversion_metrics", {
    p_auth_id: authId,
    p_start_date: dateRange.start,
    p_end_date: dateRange.end
  });

  return conversions || {
    conversionsByIntent: [],
    conversionsByUrgency: [],
    revenueAttribution: 0,
    avgOrderValue: 0
  };
}

/**
 * Generate optimization recommendations based on analytics
 * @param {string} authId - Business owner's auth ID
 * @param {Object} analyticsData - Analytics data
 * @returns {Array} Optimization recommendations
 */
async function generateOptimizationRecommendations(authId, analyticsData) {
  const recommendations = [];
  const { overviewMetrics, performanceByIntent, templatePerformance, timingAnalysis } = analyticsData;

  // Response rate optimization
  if (overviewMetrics.responseRate < 0.3) {
    recommendations.push({
      type: 'response_rate',
      priority: 'high',
      title: 'Improve Response Rate',
      description: `Your current response rate is ${(overviewMetrics.responseRate * 100).toFixed(1)}%. Consider personalizing messages more or adjusting timing.`,
      suggestions: [
        'Use more personalized templates',
        'Test different sending times',
        'Reduce follow-up frequency for low-engagement contacts'
      ]
    });
  }

  // Intent-based optimization
  const lowPerformingIntents = performanceByIntent.filter(intent => intent.response_rate < 0.2);
  if (lowPerformingIntents.length > 0) {
    recommendations.push({
      type: 'intent_optimization',
      priority: 'medium',
      title: 'Optimize Low-Performing Intent Categories',
      description: `Some intent categories have low response rates: ${lowPerformingIntents.map(i => i.intent_category).join(', ')}`,
      suggestions: [
        'Review and improve templates for these intents',
        'Consider different follow-up timing',
        'Add more context-specific content'
      ]
    });
  }

  // Template optimization
  const bestTemplate = templatePerformance.sort((a, b) => b.response_rate - a.response_rate)[0];
  const worstTemplate = templatePerformance.sort((a, b) => a.response_rate - b.response_rate)[0];
  
  if (bestTemplate && worstTemplate && bestTemplate.response_rate > worstTemplate.response_rate * 1.5) {
    recommendations.push({
      type: 'template_optimization',
      priority: 'medium',
      title: 'Template Performance Variation',
      description: `Template "${bestTemplate.variant}" performs ${((bestTemplate.response_rate / worstTemplate.response_rate - 1) * 100).toFixed(0)}% better than "${worstTemplate.variant}"`,
      suggestions: [
        `Use more "${bestTemplate.variant}" style templates`,
        `Review and improve "${worstTemplate.variant}" templates`,
        'Run A/B tests with top-performing template styles'
      ]
    });
  }

  // Timing optimization
  if (timingAnalysis.optimalHours && timingAnalysis.optimalHours.length > 0) {
    const optimalHours = timingAnalysis.optimalHours.slice(0, 3);
    recommendations.push({
      type: 'timing_optimization',
      priority: 'low',
      title: 'Optimize Send Times',
      description: `Best response times are at ${optimalHours.join(', ')} hours`,
      suggestions: [
        `Schedule more follow-ups during ${optimalHours.join(', ')} hours`,
        'Avoid sending during low-response periods',
        'Consider customer time zones'
      ]
    });
  }

  return recommendations;
}

/**
 * Get date range object based on time range string
 * @param {string} timeRange - Time range (7d, 30d, 90d, etc.)
 * @returns {Object} Date range object
 */
function getDateRange(timeRange) {
  const end = new Date();
  const start = new Date();
  
  const days = parseInt(timeRange.replace('d', ''));
  start.setDate(start.getDate() - days);
  
  return {
    start: start.toISOString(),
    end: end.toISOString()
  };
}

/**
 * Track follow-up outcome for analytics
 * @param {string} followUpId - Follow-up ID
 * @param {string} authId - Business owner's auth ID
 * @param {string} outcome - Outcome type (sent, responded, converted)
 * @param {Object} metadata - Additional metadata
 */
export async function trackFollowUpOutcome(followUpId, authId, outcome, metadata = {}) {
  try {
    await supabase.rpc("track_follow_up_outcome", {
      p_follow_up_id: followUpId,
      p_auth_id: authId,
      p_outcome: outcome,
      p_metadata: metadata,
      p_timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error("Error tracking follow-up outcome:", error);
  }
}

/**
 * Get real-time follow-up performance dashboard data
 * @param {string} authId - Business owner's auth ID
 * @returns {Object} Dashboard data
 */
export async function getFollowUpDashboard(authId) {
  try {
    const [todayMetrics, weeklyTrend, activeFollowUps] = await Promise.all([
      getTodayMetrics(authId),
      getWeeklyTrend(authId),
      getActiveFollowUps(authId)
    ]);

    return {
      success: true,
      today: todayMetrics,
      weeklyTrend,
      activeFollowUps
    };
  } catch (error) {
    logger.error("Error getting follow-up dashboard:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Get today's follow-up metrics
 * @param {string} authId - Business owner's auth ID
 * @returns {Object} Today's metrics
 */
async function getTodayMetrics(authId) {
  const today = new Date().toISOString().split('T')[0];
  
  const { data: metrics } = await supabase.rpc("get_daily_follow_up_metrics", {
    p_auth_id: authId,
    p_date: today
  });

  return metrics || {
    scheduled: 0,
    sent: 0,
    responded: 0,
    pending: 0
  };
}

/**
 * Get weekly trend data
 * @param {string} authId - Business owner's auth ID
 * @returns {Array} Weekly trend data
 */
async function getWeeklyTrend(authId) {
  const { data: trend } = await supabase.rpc("get_weekly_follow_up_trend", {
    p_auth_id: authId
  });

  return trend || [];
}

/**
 * Get active follow-ups summary
 * @param {string} authId - Business owner's auth ID
 * @returns {Object} Active follow-ups summary
 */
async function getActiveFollowUps(authId) {
  const { data: active } = await supabase.rpc("get_active_follow_ups_summary", {
    p_auth_id: authId
  });

  return active || {
    totalActive: 0,
    byPriority: {},
    nextDue: null
  };
}
